# LargemouthBass.com Project Summary

## Project Overview

LMB is a fishing community platform built with a modern tech stack:

- **Backend**: NestJS (TypeScript)
- **Database**: PostgreSQL
- **Web Frontend**: Next.js (React)
- **Mobile Apps**: Expo/React Native (iOS and Android)
- **Authentication**: Social login only (Google, Facebook, GitHub, Apple)

## Core Features

- User profiles and social connections
- Fishing communities with moderation
- Fishing spots with location data
- Posts, comments, and content sharing
- Guide and vendor profiles
- Content moderation and reporting
- Analytics and activity tracking
- Real-time notifications

## Project Structure

This is a monorepo organized into shared code and application directories:

- **types/**: Shared TypeScript types and interfaces for all applications
- **server/**: NestJS backend API with PostgreSQL integration
- **mobile/**: Expo/React Native mobile applications for iOS and Android
- **web/**: Next.js web application with SSR and optimized SEO

## Architecture Approach

### Backend (NestJS)

The NestJS backend serves as a unified API for both web and mobile clients, providing:

- RESTful API endpoints with versioning
- JWT-based authentication
- Real-time WebSocket connections for notifications
- Database access layer with repositories
- Business logic and validation

### Web Frontend (Next.js)

The Next.js web application provides:

- Server-side rendering for optimal SEO
- Static site generation for performance
- Responsive design for all devices
- Progressive Web App capabilities
- Shared UI components with mobile when possible

### Mobile Apps (Expo/React Native)

The Expo-based mobile applications deliver:

- Native iOS and Android experiences
- Offline capabilities
- Push notifications
- Location services for fishing spots
- Camera integration for content sharing

### Shared Code

A monorepo approach allows sharing:

- TypeScript types and interfaces
- API client code
- Utility functions
- Business logic
- UI components where appropriate

## Authentication Approach

- **Social-only authentication** (no email/password)
- **HttpOnly cookie authentication** for secure web sessions
- **JWT tokens** with dual support (Authorization headers + cookies)
- **Custom NestJS OAuth endpoints** with GitHub integration
- **NextJS authentication context** for frontend state management
- **Cross-origin cookie support** for development and production
- **Enterprise-grade CSRF protection** for all state-changing requests
- **Comprehensive security headers** with Helmet middleware

## Data Model

The application uses a comprehensive type system defined in shared types, including:

- User management (profiles, preferences, activity)
- Content types (posts, comments, fishing spots)
- Community features (membership, roles, moderation)
- Authentication (sessions, tokens, providers)
- Security (audit logs, rate limiting, device tracking)

## Development Approach

1. **TypeScript Throughout**: Consistent typing across all platforms
2. **Feature-Based Structure**: Organize code by domain features
3. **API-First Development**: Define and implement API contracts before UI
4. **Shared Code**: Maximize code reuse between platforms
5. **Progressive Enhancement**: Build core features first, then enhance

## Security Considerations

- **CSRF Protection**: Enterprise-grade CSRF token validation for all state-changing requests
- **Security Headers**: Comprehensive HTTP security headers via Helmet middleware
- **Token Security**: Tokens stored separately from user data in httpOnly cookies
- **Session Security**: Sessions don't contain sensitive user information
- **Audit Logging**: Comprehensive security event logging and monitoring
- **Device Management**: Session control and device tracking for security
- **Rate Limiting**: Multi-layer rate limiting to prevent abuse and attacks
- **Input Validation**: Comprehensive request validation and sanitization
- **OWASP Compliance**: Protection against OWASP Top 10 security vulnerabilities

## Development Setup

### Prerequisites

- Node.js and pnpm
- PostgreSQL instance running locally or remotely
- Expo CLI for mobile development

### Database Setup

1. **Install PostgreSQL** and create a database named `lmb`
2. **Create a database user** (e.g., `lmbuser`) with appropriate permissions
3. **Grant permissions** to the user:
   ```sql
   GRANT ALL PRIVILEGES ON SCHEMA public TO lmbuser;
   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO lmbuser;
   GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO lmbuser;
   ```

### Environment Variables

Create a `.env` file in the `server/` directory with:

```env
# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=lmb
DATABASE_USER=lmbuser
DATABASE_PASSWORD=your_password_here

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# GitHub OAuth Configuration
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_CALLBACK_URL=http://localhost:3000/v1/auth/github/callback

# Application Configuration
NODE_ENV=development
PORT=3000
APP_URL=http://localhost:3000

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=10

# Web Frontend URL (for OAuth redirects)
WEB_URL=http://localhost:3001
```

### Database Schema Setup

After setting up PostgreSQL and environment variables:

```bash
cd server

# Install dependencies
pnpm install

# Initialize database schema
curl -X POST http://localhost:3000/v1/init-db

# Or run migrations (alternative approach)
pnpm migration:run
```

### Running the Applications

```bash
# Start the backend server
cd server
pnpm start:dev

# Start the web application
cd web
pnpm dev

# Start the mobile application (when implemented)
cd mobile
pnpm start
```

## API Endpoints

### Authentication Endpoints

- **`GET /v1/auth/github`** - Initiate GitHub OAuth flow
- **`GET /v1/auth/github/callback`** - GitHub OAuth callback handler
- **`GET /v1/auth/profile`** - Get authenticated user profile (JWT protected)
- **`POST /v1/auth/logout`** - User logout (CSRF protected)
- **`GET /v1/auth/health`** - Authentication service health check

### Security Endpoints

- **`GET /v1/security/csrf-token`** - Generate CSRF token for state-changing requests
  - Returns: `{"csrfToken": "...", "message": "CSRF token generated successfully"}`
  - Sets httpOnly cookie with CSRF secret (24-hour expiration)

### User Management Endpoints

- **`GET /v1/users/me`** - Get current user's full profile (JWT protected)
- **`PUT /v1/users/me`** - Update current user profile (JWT + CSRF protected)
- **`GET /v1/users/:id`** - Get public user profile (accepts both user ID and username)
- **`GET /v1/users/search`** - Search users by username/display name with pagination
- **`GET /v1/users/me/followers`** - Get current user's followers (JWT protected)
- **`GET /v1/users/me/following`** - Get who current user follows (JWT protected)
- **`GET /v1/users/:userId/followers`** - Get user's followers
- **`GET /v1/users/:userId/following`** - Get who user follows
- **`POST /v1/users/:userId/follow`** - Follow another user (JWT + CSRF protected)
- **`DELETE /v1/users/:userId/follow`** - Unfollow user (JWT + CSRF protected)

### System Endpoints

- **`GET /v1/health`** - Overall system health check
- **`POST /v1/init-db`** - Initialize database schema (development only)

### Security Features

- **CSRF Protection**: All POST/PUT/PATCH/DELETE requests require valid CSRF tokens
- **Rate Limiting**: Configurable throttling on all endpoints
- **Security Headers**: Comprehensive HTTP security headers via Helmet
- **JWT Authentication**: Secure token-based authentication with httpOnly cookies
- **OAuth Integration**: Secure social authentication with GitHub (more providers coming)

## Implementation Status

### ✅ Completed

- Project structure and architecture planning
- Technology selection and evaluation
- **PostgreSQL database migration** from SurrealDB
- **Complete database schema** with 13+ tables supporting graph relationships
- **TypeORM integration** with proper entity definitions and migrations
- **GitHub OAuth authentication** working end-to-end with httpOnly cookies
- **NextJS web frontend** with complete authentication flow
- **Shared types system** using symlinks for code reuse
- **Database migration system** for ongoing schema management
- **Comprehensive type system** with proper TypeScript enums
- **Repository pattern** implementation with TypeORM
- **Cross-origin authentication** with cookie-parser middleware
- **Enterprise CSRF protection** with automatic token management
- **Security headers** via Helmet middleware for production-ready security
- **OWASP Top 10 compliance** with comprehensive security measures
- **Users Module** with complete user management, profile, and social following features
- **Communities Module** with full community management, membership system, and invitation features

### 🚧 In Progress

- Implementing additional OAuth providers (Google, Facebook, Apple)
- Core API endpoints for posts and comments

### 📋 Next Steps

1. ~~Implement Communities Module (community creation, membership, moderation)~~ ✅ **COMPLETED**
2. Implement Posts Module (content creation, voting, tagging)
3. Implement Comments Module (threaded discussions, voting)
4. Implement additional social authentication providers (Google, Facebook, Apple)
5. Create basic web UI with Next.js
6. Build mobile app foundation with Expo
7. Implement graph-based social features (friend suggestions, content recommendations)
8. Add geospatial queries for fishing spot discovery
