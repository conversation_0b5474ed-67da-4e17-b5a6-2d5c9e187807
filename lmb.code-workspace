{
  "folders": [
    {
      "name": "lmb",
      "path": "."
    }
  ],
  "settings": {
    // These settings apply to all folders in this workspace
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
      "source.organizeImports": "never"
    },
    // ESLint specific: Tell ESLint where to find project roots/configs
    "eslint.workingDirectories": [
      { "pattern": "server/" },
      { "pattern": "web/" },
      { "pattern": "mobile/" }
    ]
    // Add any other common settings here (e.g., specific file exclusions, etc.)
  }
}
