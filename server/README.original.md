# LMB Project Summary

## Project Overview

LMB is a fishing community platform built with a modern tech stack:

- **Backend**: NestJS (TypeScript)
- **Database**: SurrealDB
- **Frontend**: Expo/React Native (for iOS, Android, and Web)
- **Authentication**: Social login only (Google, Facebook, GitHub, Apple)

## Core Features

- User profiles and social connections
- Fishing communities with moderation
- Fishing spots with location data
- Posts, comments, and content sharing
- Guide and vendor profiles
- Content moderation and reporting
- Analytics and activity tracking
- Real-time notifications

## Authentication Approach

- **Social-only authentication** (no email/password)
- **Expo Auth Session** for client-side auth flows
- **Custom NestJS endpoints** for token verification
- **JWT sessions** for maintaining authentication state
- **Tokens stored separately** from user data for security

## Data Model

The application uses a comprehensive type system defined in `src/types/global.ts`, including:

- User management (profiles, preferences, activity)
- Content types (posts, comments, fishing spots)
- Community features (membership, roles, moderation)
- Authentication (sessions, tokens, providers)
- Security (audit logs, rate limiting, device tracking)

## File Structure

```
src/
├── main.ts # Application entry point
├── app.module.ts # Root module
├── app.controller.ts # Root controller
├── app.service.ts # Root service
├── types/ # Type definitions
│ └── global.ts # Global type definitions
├── config/ # Configuration
│ ├── config.module.ts # Configuration module
│ ├── config.service.ts # Environment and app config
│ └── validation.schema.ts # Config validation schema
├── auth/ # Authentication
│ ├── auth.module.ts # Auth module
│ ├── auth.controller.ts # Auth endpoints
│ ├── auth.service.ts # Auth business logic
│ ├── guards/ # Auth guards
│ │ ├── jwt.guard.ts # JWT authentication guard
│ │ └── roles.guard.ts # Role-based authorization
│ ├── strategies/ # Auth strategies
│ │ ├── google.strategy.ts # Google OAuth strategy
│ │ ├── facebook.strategy.ts # Facebook OAuth strategy
│ │ └── jwt.strategy.ts # JWT strategy
│ └── dto/ # Data transfer objects
│ └── auth.dto.ts # Auth-related DTOs
├── users/ # User management
│ ├── users.module.ts # Users module
│ ├── users.controller.ts # User endpoints
│ ├── users.service.ts # User business logic
│ └── dto/ # Data transfer objects
│ └── user.dto.ts # User-related DTOs
├── communities/ # Community features
│ ├── communities.module.ts # Communities module
│ ├── communities.controller.ts # Community endpoints
│ ├── communities.service.ts # Community business logic
│ └── dto/ # Data transfer objects
│ └── community.dto.ts # Community-related DTOs
├── posts/ # Post management
│ ├── posts.module.ts # Posts module
│ ├── posts.controller.ts # Post endpoints
│ ├── posts.service.ts # Post business logic
│ └── dto/ # Data transfer objects
│ └── post.dto.ts # Post-related DTOs
├── comments/ # Comment management
│ ├── comments.module.ts # Comments module
│ ├── comments.controller.ts # Comment endpoints
│ ├── comments.service.ts # Comment business logic
│ └── dto/ # Data transfer objects
│ └── comment.dto.ts # Comment-related DTOs
├── fishing-spots/ # Fishing spot features
│ ├── fishing-spots.module.ts # Fishing spots module
│ ├── fishing-spots.controller.ts # Fishing spot endpoints
│ ├── fishing-spots.service.ts # Fishing spot business logic
│ └── dto/ # Data transfer objects
│ └── fishing-spot.dto.ts # Fishing spot DTOs
├── guides/ # Guide features
│ ├── guides.module.ts # Guides module
│ ├── guides.controller.ts # Guide endpoints
│ ├── guides.service.ts # Guide business logic
│ └── dto/ # Data transfer objects
│ └── guide.dto.ts # Guide-related DTOs
├── vendors/ # Vendor features
│ ├── vendors.module.ts # Vendors module
│ ├── vendors.controller.ts # Vendor endpoints
│ ├── vendors.service.ts # Vendor business logic
│ └── dto/ # Data transfer objects
│ └── vendor.dto.ts # Vendor-related DTOs
├── notifications/ # Notification system
│ ├── notifications.module.ts # Notifications module
│ ├── notifications.controller.ts # Notification endpoints
│ ├── notifications.service.ts # Notification business logic
│ ├── notifications.gateway.ts # WebSocket gateway for real-time notifications
│ └── dto/ # Data transfer objects
│ └── notification.dto.ts # Notification DTOs
├── reports/ # Content reporting
│ ├── reports.module.ts # Reports module
│ ├── reports.controller.ts # Report endpoints
│ ├── reports.service.ts # Report business logic
│ └── dto/ # Data transfer objects
│ └── report.dto.ts # Report-related DTOs
├── analytics/ # Analytics tracking
│ ├── analytics.module.ts # Analytics module
│ ├── analytics.controller.ts # Analytics endpoints
│ ├── analytics.service.ts # Analytics business logic
│ └── dto/ # Data transfer objects
│ └── analytics.dto.ts # Analytics-related DTOs
├── tags/ # Content tagging
│ ├── tags.module.ts # Tags module
│ ├── tags.controller.ts # Tag endpoints
│ ├── tags.service.ts # Tag business logic
│ └── dto/ # Data transfer objects
│ └── tag.dto.ts # Tag-related DTOs
├── common/ # Shared code
│ ├── decorators/ # Custom decorators
│ │ ├── roles.decorator.ts # Roles decorator
│ │ └── user.decorator.ts # Current user decorator
│ ├── filters/ # Exception filters
│ │ └── http-exception.filter.ts # HTTP exception filter
│ ├── guards/ # Guards
│ │ └── throttle.guard.ts # Rate limiting guard
│ ├── interceptors/ # Interceptors
│ │ ├── logging.interceptor.ts # Logging interceptor
│ │ └── transform.interceptor.ts # Response transformation
│ ├── middleware/ # Middleware
│ │ └── logger.middleware.ts # Request logging middleware
│ └── pipes/ # Pipes
│ └── validation.pipe.ts # Request validation pipe
└── database/ # Database integration
├── database.module.ts # Database module
├── database.service.ts # SurrealDB service
├── migrations/ # Database migrations
│ └── initial.surrealql # Initial schema (for later)
└── repositories/ # Data access layer
├── user.repository.ts # User data access
├── post.repository.ts # Post data access
└── community.repository.ts # Community data access
```

## Development Approach

1. **Start Schemaless**: Begin with a schemaless database approach for rapid development
2. **TypeScript as Source of Truth**: Use TypeScript types to define the data model
3. **Feature-Based Structure**: Organize code by domain features rather than technical layers
4. **Add Schema Later**: Implement SurrealDB schemas as the data model stabilizes
5. **Gradual Implementation**: Add schemas for one table at a time as needed

## Security Considerations

- Tokens stored separately from user data
- Sessions don't contain sensitive user information
- Comprehensive audit logging
- Device management for session control
- Rate limiting to prevent abuse
- Separate types for security-critical features

## Next Steps

1. Set up the basic NestJS project structure
2. Implement the SurrealDB connection service
3. Create the authentication module with social providers
4. Develop core user management features
5. Build community and content features
6. Implement the mobile client with Expo
7. Add analytics and reporting
8. Polish and test the application
