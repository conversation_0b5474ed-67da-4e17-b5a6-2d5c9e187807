const { Client } = require('pg');
require('dotenv').config();

async function setupMigrations() {
  const client = new Client({
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    user: process.env.DATABASE_USER || 'lmbuser',
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME || 'lmb',
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Create migrations table if it doesn't exist
    await client.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        timestamp BIGINT NOT NULL,
        name VARCHAR(255) NOT NULL
      )
    `);
    console.log('Migrations table created/verified');

    // Check if our initial migration is already recorded
    const result = await client.query(
      'SELECT * FROM migrations WHERE name = $1',
      ['InitialSchema1737000000000']
    );

    if (result.rows.length === 0) {
      // Insert the initial migration record
      await client.query(
        'INSERT INTO migrations (timestamp, name) VALUES ($1, $2)',
        [1737000000000, 'InitialSchema1737000000000']
      );
      console.log('Initial migration record inserted');
    } else {
      console.log('Initial migration already recorded');
    }

    console.log('Migration setup complete!');
  } catch (error) {
    console.error('Error setting up migrations:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

setupMigrations();
