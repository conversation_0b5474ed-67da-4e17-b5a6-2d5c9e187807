const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function setupDatabase() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'lmb',
    user: 'lmbuser',
    password: 'wr7igjls_o49uP-Riutq',
  });

  try {
    await client.connect();
    console.log('Connected to PostgreSQL');

    // Read the SQL file
    const sqlPath = path.join(__dirname, 'db.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // Execute the SQL
    console.log('Executing database schema...');
    await client.query(sql);
    console.log('Database schema created successfully!');

  } catch (error) {
    console.error('Error setting up database:', error);
  } finally {
    await client.end();
  }
}

setupDatabase();
