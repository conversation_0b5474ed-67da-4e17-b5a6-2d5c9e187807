import { Controller, Get, Post } from '@nestjs/common';
import { AppService } from './app.service';
import { Public } from './common/decorators/public.decorator';
import { DatabaseService } from './database/database.service';
import { DataSource } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly databaseService: DatabaseService,
    private readonly dataSource: DataSource,
  ) { }

  @Get()
  @Public()
  getHello (): string {
    return this.appService.getHello();
  }

  @Get('v1/health')
  @Public()
  async healthCheck () {
    const dbHealthy = await this.databaseService.healthCheck();

    return {
      status: dbHealthy ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      service: 'lmb-api',
      database: dbHealthy ? 'connected' : 'disconnected',
      version: '1.0.0',
      apiVersion: 'v1',
    };
  }

  @Post('v1/init-db')
  @Public()
  async initializeDatabase () {
    try {
      // Read and execute the SQL file
      const sqlPath = path.join(__dirname, '..', 'db.sql');
      const sql = fs.readFileSync(sqlPath, 'utf8');

      // Split SQL into individual statements and execute them
      const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);

      for (const statement of statements) {
        if (statement.trim()) {
          await this.dataSource.query(statement.trim());
        }
      }

      return {
        status: 'success',
        message: 'Database schema created successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
