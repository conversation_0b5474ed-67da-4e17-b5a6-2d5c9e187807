import {
  Controller,
  Get,
  Post,
  UseGuards,
  Req,
  <PERSON><PERSON>,
  Lo<PERSON>,
  HttpStatus,
} from '@nestjs/common';
import { Public } from '../common/decorators/public.decorator';
import { SkipCsrf } from '../security/decorators/skip-csrf.decorator';
import { Request, Response } from 'express';
import { AuthService } from './auth.service';
import { GitHubAuthGuard } from './guards/github.guard';
import { JwtAuthGuard } from './guards/jwt.guard';
import { User } from '@/types/global';
import { LoginResponseDto } from './dto/auth.dto';
import { AppConfigService } from '../config/config.service';

@Controller('v1/auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private authService: AuthService,
    private configService: AppConfigService,
  ) { }

  @Get('github')
  @Public()
  @SkipCsrf()
  @UseGuards(GitHubAuthGuard)
  async githubAuth (@Req() req: Request) {
    // Initiates GitHub OAuth flow
    // The guard will redirect to GitHub
    // Note: The state parameter is handled by the GitHubAuthGuard
  }

  @Get('github/callback')
  @Public()
  @SkipCsrf()
  @UseGuards(GitHubAuthGuard)
  async githubCallback (@Req() req: Request, @Res() res: Response) {
    try {
      const user = req.user as User;
      const loginResponse = await this.authService.login(user);

      // Check if this is a web app request
      // In development, default to web app behavior
      // In production, check for browser-like headers or explicit web parameter
      const userAgent = req.get('user-agent') || '';
      const acceptHeader = req.get('accept') || '';
      const referer = req.get('referer') || '';

      const isBrowserRequest = userAgent.includes('Mozilla') || acceptHeader.includes('text/html');
      const isWebApp = this.configService.isDevelopment ||
        isBrowserRequest ||
        referer.includes('localhost:3001') ||
        req.query.web === 'true';

      if (isWebApp) {
        // For web app: set httpOnly cookie and redirect to callback page
        res.cookie('auth-token', loginResponse.token, {
          httpOnly: true,
          secure: this.configService.isProduction,
          sameSite: 'lax', // Changed from 'strict' to 'lax' for cross-origin redirects
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
          path: '/', // Explicitly set path
        });

        const webUrl = process.env.WEB_URL || 'http://localhost:3001';
        return res.redirect(`${webUrl}/auth/github/callback`);
      } else {
        // For API/mobile: return JSON
        if (this.configService.isDevelopment) {
          return res.json(loginResponse);
        } else {
          // Redirect to frontend with token in production
          const frontendUrl = `${this.configService.appUrl}/auth/success?token=${loginResponse.token}`;
          return res.redirect(frontendUrl);
        }
      }
    } catch (error) {
      this.logger.error('GitHub callback failed:', error);
      this.logger.error('Error stack:', error.stack);

      // Check if this is a web app request for error handling
      const userAgent = req.get('user-agent') || '';
      const acceptHeader = req.get('accept') || '';
      const referer = req.get('referer') || '';

      const isBrowserRequest = userAgent.includes('Mozilla') || acceptHeader.includes('text/html');
      const isWebApp = this.configService.isDevelopment ||
        isBrowserRequest ||
        referer.includes('localhost:3001') ||
        req.query.web === 'true';

      if (isWebApp) {
        const webUrl = process.env.WEB_URL || 'http://localhost:3001';
        return res.redirect(
          `${webUrl}/auth/github/callback?error=${encodeURIComponent(error.message)}`,
        );
      } else {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          message: 'Authentication failed',
          error: error.message,
          stack: this.configService.isDevelopment ? error.stack : undefined,
        });
      }
    }
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  async getProfile (@Req() req: Request) {
    const user = req.user as User;
    return {
      id: user.id,
      uname: user.uname,
      email: user.email,
      profile: user.profile,
      status: user.status,
      emailVerified: user.emailVerified,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      providerAccounts: user.providerAccounts,
    };
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  async logout (@Req() req: Request, @Res() res: Response) {
    try {
      const user = req.user as User;

      // Get token from Authorization header or cookie
      const authHeader = req.get('authorization');
      const token =
        authHeader?.replace('Bearer ', '') || req.cookies['auth-token'];

      if (token) {
        // Use the new logout method that revokes OAuth tokens
        await this.authService.logoutWithTokenRevocation(token);
      }

      // Clear the httpOnly cookie
      res.clearCookie('auth-token', {
        httpOnly: true,
        secure: this.configService.isProduction,
        sameSite: 'lax', // Match the sameSite setting from login
        path: '/', // Explicitly set path to match login
      });

      return res.json({ message: 'Logged out successfully' });
    } catch (error) {
      this.logger.error('Logout failed:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Logout failed',
        error: error.message,
      });
    }
  }

  @Get('health')
  @Public()
  @SkipCsrf()
  async healthCheck () {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'auth',
    };
  }


}
