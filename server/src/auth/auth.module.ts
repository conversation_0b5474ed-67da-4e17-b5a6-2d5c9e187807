import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { GitHubStrategy } from './strategies/github.strategy';
import { JwtStrategy } from './strategies/jwt.strategy';
import { JwtAuthGuard } from './guards/jwt.guard';
import { GitHubAuthGuard } from './guards/github.guard';
import { UserRepository } from '../database/repositories/user.repository';
import { UsernameGeneratorService } from '../services/username-generator.service';
import { AppConfigService } from '../config/config.service';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      inject: [AppConfigService],
      useFactory: (configService: AppConfigService) => ({
        secret: configService.jwtSecret,
        signOptions: {
          expiresIn: configService.jwtExpiresIn,
        },
      }),
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    GitHubStrategy,
    JwtStrategy,
    JwtAuthGuard,
    GitHubAuthGuard,
    UserRepository,
    UsernameGeneratorService,
  ],
  exports: [AuthService, JwtAuthGuard],
})
export class AuthModule { }
