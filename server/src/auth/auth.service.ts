import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserRepository } from '../database/repositories/user.repository';
import { UsernameGeneratorService } from '../services/username-generator.service';
import { User, UserStatus, AuthToken } from '@/types/global';
import { LoginResponseDto } from './dto/auth.dto';
import { AppConfigService } from '../config/config.service';
import axios from 'axios';

// JWT Payload interface
interface JwtPayload {
  sub: string;
  email: string;
}

// GitHub user interface for OAuth
interface GitHubUserDto {
  id: string;
  login: string;
  name?: string;
  email: string;
  avatar_url?: string;
  bio?: string;
  location?: string;
  blog?: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private userRepository: UserRepository,
    private jwtService: JwtService,
    private usernameGeneratorService: UsernameGeneratorService,
    private configService: AppConfigService,
  ) { }

  async validateGitHubUser (
    githubUser: GitHubUserDto,
    accessToken: string,
    refreshToken?: string,
  ): Promise<User> {
    try {
      this.logger.log(`Validating GitHub user: ${JSON.stringify(githubUser)}`);

      // Check if user exists by GitHub provider account
      let user = await this.userRepository.findUserByProviderAccount(
        'github',
        githubUser.id,
      );

      this.logger.log(`Found existing user: ${user ? 'YES' : 'NO'}`);
      if (user) {
        this.logger.log(`User ID: ${user.id}`);
      }

      if (user && user.id) {
        // Update last login and user info
        this.logger.log(`Updating existing user: ${user.id}`);
        user = await this.userRepository.updateUser(user.id, {
          lastLoginAt: new Date(),
          profile: {
            ...user.profile,
            displayName: githubUser.name || user.profile?.displayName || githubUser.login,
            pic: githubUser.avatar_url || user.profile?.pic || '',
            bio: githubUser.bio || user.profile?.bio || '',
            location: githubUser.location || user.profile?.location || '',
            website: githubUser.blog || user.profile?.website || '',
          },
        });

        // Update or create auth token
        await this.updateOrCreateAuthToken(
          user.id,
          'github',
          githubUser.id,
          accessToken,
          refreshToken,
        );
      } else {
        // Check if user exists by email
        const existingUser = await this.userRepository.findUserByEmail(
          githubUser.email,
        );

        if (existingUser) {
          // Link GitHub account to existing user
          user = await this.userRepository.updateUser(existingUser.id, {
            providerAccounts: {
              ...existingUser.providerAccounts,
              github: githubUser.id,
            },
            lastLoginAt: new Date(),
          });

          // Create auth token
          await this.updateOrCreateAuthToken(
            user.id,
            'github',
            githubUser.id,
            accessToken,
            refreshToken,
          );
        } else {
          // Create new user
          this.logger.log('Creating new user from GitHub profile');
          user = await this.createUserFromGitHub(githubUser);
          this.logger.log(`Created new user with ID: ${user.id}`);

          // Create auth token
          await this.updateOrCreateAuthToken(
            user.id,
            'github',
            githubUser.id,
            accessToken,
            refreshToken,
          );
        }
      }

      return user;
    } catch (error) {
      this.logger.error('Failed to validate GitHub user:', error);
      throw error;
    }
  }

  private async createUserFromGitHub (githubUser: GitHubUserDto): Promise<User> {
    const username = await this.usernameGeneratorService.generateUsername();

    const userData: Partial<User> = {
      uname: username,
      email: githubUser.email,
      emailVerified: true, // GitHub emails are considered verified
      status: 'Active' as UserStatus,
      profile: {
        displayName: githubUser.name || githubUser.login,
        bio: githubUser.bio || '',
        location: githubUser.location || '',
        website: githubUser.blog || '',
        pic: githubUser.avatar_url || '',
        banner: '',
        isGuide: false,
        isVendor: false,
      },
      prefs: {
        theme: 'System',
        notificationPreferences: [],
        emailDigest: 'Weekly',
        contentVisibility: 'Public',
        twoFactorEnabled: false,
        sessionTimeout: 'Medium',
      },
      activity: {
        postsToday: 0,
        commentsToday: 0,
        votesToday: 0,
        lastActivityAt: new Date(),
      },
      savedContent: [],
      subscribedCommunities: [],
      providerAccounts: {
        github: githubUser.id,
      },
    };

    return await this.userRepository.createUser(userData);
  }



  private async updateOrCreateAuthToken (
    userId: string,
    provider: string,
    providerUserId: string,
    accessToken: string,
    refreshToken?: string,
  ): Promise<AuthToken> {
    const existingToken = await this.userRepository.findAuthTokenByUserAndProvider(
      userId,
      provider,
    );

    const tokenData = {
      userId,
      provider: provider as any,
      providerUserId,
      accessToken,
      refreshToken,
      expiresAt: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
    };

    if (existingToken) {
      return await this.userRepository.updateAuthToken(existingToken.id, tokenData);
    } else {
      return await this.userRepository.createAuthToken(tokenData);
    }
  }

  async login (user: User): Promise<LoginResponseDto> {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
    };

    const token = this.jwtService.sign(payload);

    // Create session
    await this.userRepository.createSession({
      userId: user.id,
      token,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });

    return {
      token,
      user: {
        id: user.id,
        uname: user.uname,
        email: user.email,
        profile: {
          displayName: user.profile.displayName,
          pic: user.profile.pic,
          bio: user.profile.bio,
          location: user.profile.location,
          website: user.profile.website,
        },
      },
    };
  }

  async validateUser (userId: string): Promise<User> {
    const user = await this.userRepository.findUserById(userId);

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    if (user.status === 'Banned' || user.status === 'Deleted') {
      throw new UnauthorizedException('Account access denied');
    }

    return user;
  }

  async logout (token: string): Promise<void> {
    // Find and delete the session
    const session = await this.userRepository.findSessionByToken(token);
    if (session) {
      await this.userRepository.deleteSession(session.id);
    }
  }

  async logoutWithTokenRevocation (token: string): Promise<void> {
    // Find the session to get the user
    const session = await this.userRepository.findSessionByToken(token);
    if (!session) {
      this.logger.warn('Session not found for token during logout');
      return;
    }

    try {
      // Get user's GitHub auth token
      const githubToken = await this.userRepository.findAuthTokenByUserAndProvider(
        session.userId,
        'github'
      );

      if (githubToken && githubToken.accessToken) {
        // Revoke the GitHub OAuth token
        await this.revokeGitHubToken(githubToken.accessToken);

        // Delete the stored auth token
        await this.userRepository.deleteAuthToken(githubToken.id);
        this.logger.log(`Revoked GitHub token for user ${session.userId}`);
      }

      // Delete the session
      await this.userRepository.deleteSession(session.id);
      this.logger.log(`Deleted session for user ${session.userId}`);
    } catch (error) {
      this.logger.error('Error during logout with token revocation:', error);
      // Still delete the session even if token revocation fails
      await this.userRepository.deleteSession(session.id);
      throw error;
    }
  }

  private async revokeGitHubToken (accessToken: string): Promise<void> {
    try {
      // GitHub's token revocation endpoint
      // https://docs.github.com/en/rest/apps/oauth-applications#delete-an-app-authorization
      const response = await axios.delete(
        `https://api.github.com/applications/${this.configService.githubClientId}/grant`,
        {
          auth: {
            username: this.configService.githubClientId,
            password: this.configService.githubClientSecret,
          },
          data: {
            access_token: accessToken,
          },
          headers: {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'LMB-App',
          },
        }
      );

      this.logger.log('Successfully revoked GitHub token');
    } catch (error) {
      this.logger.error('Failed to revoke GitHub token:', error.response?.data || error.message);
      // Don't throw here - we still want to complete the logout process
    }
  }
}
