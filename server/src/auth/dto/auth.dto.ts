import { IsString, IsEmail, IsOptional, IsUrl } from 'class-validator';

export class GitHubUserDto {
  @IsString()
  id: string;

  @IsString()
  login: string;

  @IsEmail()
  email: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  avatar_url?: string;

  @IsOptional()
  @IsString()
  bio?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsUrl()
  blog?: string;
}

export class AuthResponseDto {
  @IsString()
  accessToken: string;

  @IsString()
  refreshToken?: string;

  user: {
    id: string;
    uname: string;
    email: string;
    profile: {
      displayName: string;
      pic: string;
      bio?: string;
      location?: string;
      website?: string;
    };
  };
}

export class LoginResponseDto {
  @IsString()
  token: string;

  user: {
    id: string;
    uname: string;
    email: string;
    profile: {
      displayName: string;
      pic: string;
      bio?: string;
      location?: string;
      website?: string;
    };
  };
}
