import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-github2';
import { AppConfigService } from '../../config/config.service';
import { AuthService } from '../auth.service';
import { GitHubUserDto } from '../dto/auth.dto';

@Injectable()
export class GitHubStrategy extends PassportStrategy(Strategy, 'github') {
  private readonly logger = new Logger(GitHubStrategy.name);

  constructor(
    private configService: AppConfigService,
    private authService: AuthService,
  ) {
    super({
      clientID: configService.githubClientId,
      clientSecret: configService.githubClientSecret,
      callbackURL: configService.githubCallbackUrl,
      scope: ['user:email'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
  ): Promise<any> {
    try {
      this.logger.log(`GitHub OAuth validation for user: ${profile.username}`);

      const githubUser: GitHubUserDto = {
        id: profile.id,
        login: profile.username,
        email: profile.emails?.[0]?.value || '',
        name: profile.displayName || profile.username,
        avatar_url: profile.photos?.[0]?.value,
        bio: profile._json?.bio,
        location: profile._json?.location,
        blog: profile._json?.blog,
      };

      // Find or create user
      const user = await this.authService.validateGitHubUser(
        githubUser,
        accessToken,
        refreshToken,
      );

      return user;
    } catch (error) {
      this.logger.error('GitHub OAuth validation failed:', error);
      throw error;
    }
  }
}
