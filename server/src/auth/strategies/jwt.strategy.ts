import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AppConfigService } from '../../config/config.service';
import { UserRepository } from '../../database/repositories/user.repository';
import { User } from '@/types/global';

export interface JwtPayload {
  sub: string; // user id
  email: string;
  iat?: number;
  exp?: number;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private configService: AppConfigService,
    private userRepository: UserRepository,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        ExtractJwt.fromAuthHeaderAsBearerToken(),
        ExtractJwt.fromExtractors([
          (request) => {
            return request?.cookies?.['auth-token'];
          },
        ]),
      ]),
      ignoreExpiration: false,
      secretOrKey: configService.jwtSecret,
    });
  }

  async validate (payload: JwtPayload): Promise<User> {
    try {
      const user = await this.userRepository.findUserById(payload.sub);

      if (!user) {
        this.logger.warn(`User not found for JWT payload: ${payload.sub}`);
        throw new UnauthorizedException('User not found');
      }

      if (user.status === 'Banned' || user.status === 'Deleted') {
        this.logger.warn(`Access denied for user ${user.id} with status: ${user.status}`);
        throw new UnauthorizedException('Account access denied');
      }

      return user;
    } catch (error) {
      this.logger.error('JWT validation failed:', error);
      throw new UnauthorizedException('Invalid token');
    }
  }
}
