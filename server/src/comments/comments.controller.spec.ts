import { Test, TestingModule } from '@nestjs/testing';
import { CommentsController, PostCommentsController, AuthorCommentsController } from './comments.controller';
import { CommentsService } from './comments.service';
import { JwtAuthGuard } from '@/auth/guards/jwt.guard';
import { CsrfGuard } from '@/security/guards/csrf.guard';
import {
  CreateCommentDto,
  UpdateCommentDto,
  VoteCommentDto,
  CommentSearchQueryDto,
  ModerateCommentDto,
  ReplyToCommentDto,
  CommentResponseDto,
  CommentListResponseDto,
  CommentVoteStatsDto,
  CommentTreeResponseDto,
  PaginationQueryDto
} from './dto/comments.dto';
import { CommentStatus, VoteType, User, UserStatus } from '@/types/global';

describe('CommentsController', () => {
  let controller: CommentsController;
  let service: jest.Mocked<CommentsService>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'A test user',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'user.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 2,
      commentsToday: 5,
      votesToday: 15,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    lastLoginAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCommentResponse: CommentResponseDto = {
    id: 'comment-1',
    content: 'This is a test comment',
    status: CommentStatus.Published,
    author: {
      id: mockUser.id,
      uname: mockUser.uname,
      profile: mockUser.profile,
      activity: {
        postsCount: 5,
        commentsCount: 10,
        followersCount: 0,
        followingCount: 0,
      },
      createdAt: mockUser.createdAt,
    },
    post: {
      id: 'post-1',
      title: 'Test Post',
      status: 'Published',
      author: {
        id: mockUser.id,
        uname: mockUser.uname,
        profile: mockUser.profile,
        activity: {
          postsCount: 5,
          commentsCount: 10,
          followersCount: 0,
          followingCount: 0,
        },
        createdAt: mockUser.createdAt,
      },
      community: {
        id: 'community-1',
        name: 'Test Community',
      },
      createdAt: new Date(),
    },
    upvotes: 3,
    downvotes: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockJwtAuthGuard = {
    canActivate: jest.fn(() => true),
  };

  const mockCsrfGuard = {
    canActivate: jest.fn(() => true),
  };

  beforeEach(async () => {
    const mockService = {
      createComment: jest.fn(),
      getComment: jest.fn(),
      updateComment: jest.fn(),
      deleteComment: jest.fn(),
      getCommentsByPost: jest.fn(),
      getCommentsByAuthor: jest.fn(),
      getCommentTree: jest.fn(),
      replyToComment: jest.fn(),
      voteOnComment: jest.fn(),
      removeCommentVote: jest.fn(),
      getCommentVoteStats: jest.fn(),
      moderateComment: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommentsController],
      providers: [
        {
          provide: CommentsService,
          useValue: mockService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(CsrfGuard)
      .useValue(mockCsrfGuard)
      .compile();

    controller = module.get<CommentsController>(CommentsController);
    service = module.get(CommentsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createComment', () => {
    it('should create a comment', async () => {
      const createDto: CreateCommentDto = {
        content: 'This is a new comment',
        postId: 'post-1',
      };

      service.createComment.mockResolvedValue(mockCommentResponse);

      const result = await controller.createComment(createDto, { user: mockUser });

      expect(result).toEqual(mockCommentResponse);
      expect(service.createComment).toHaveBeenCalledWith(createDto, mockUser.id);
    });
  });

  describe('getComment', () => {
    it('should return a single comment', async () => {
      service.getComment.mockResolvedValue(mockCommentResponse);

      const result = await controller.getComment('comment-1', { user: mockUser });

      expect(result).toEqual(mockCommentResponse);
      expect(service.getComment).toHaveBeenCalledWith('comment-1', mockUser.id);
    });

    it('should work without authenticated user', async () => {
      service.getComment.mockResolvedValue(mockCommentResponse);

      const result = await controller.getComment('comment-1', {});

      expect(result).toEqual(mockCommentResponse);
      expect(service.getComment).toHaveBeenCalledWith('comment-1', undefined);
    });
  });

  describe('updateComment', () => {
    it('should update a comment', async () => {
      const updateDto: UpdateCommentDto = {
        content: 'Updated comment content',
      };

      const updatedComment = { ...mockCommentResponse, ...updateDto };
      service.updateComment.mockResolvedValue(updatedComment);

      const result = await controller.updateComment('comment-1', updateDto, { user: mockUser });

      expect(result).toEqual(updatedComment);
      expect(service.updateComment).toHaveBeenCalledWith('comment-1', updateDto, mockUser.id);
    });
  });

  describe('deleteComment', () => {
    it('should delete a comment', async () => {
      service.deleteComment.mockResolvedValue(undefined);

      await controller.deleteComment('comment-1', { user: mockUser });

      expect(service.deleteComment).toHaveBeenCalledWith('comment-1', mockUser.id);
    });
  });

  describe('voteOnComment', () => {
    it('should vote on a comment', async () => {
      const voteDto: VoteCommentDto = { voteType: VoteType.Upvote };
      const expectedStats: CommentVoteStatsDto = {
        upvotes: 4,
        downvotes: 0,
        userVote: VoteType.Upvote,
      };

      service.voteOnComment.mockResolvedValue(expectedStats);

      const result = await controller.voteOnComment('comment-1', voteDto, { user: mockUser });

      expect(result).toEqual(expectedStats);
      expect(service.voteOnComment).toHaveBeenCalledWith('comment-1', voteDto, mockUser.id);
    });
  });

  describe('removeCommentVote', () => {
    it('should remove vote from a comment', async () => {
      const expectedStats: CommentVoteStatsDto = {
        upvotes: 2,
        downvotes: 0,
      };

      service.removeCommentVote.mockResolvedValue(expectedStats);

      const result = await controller.removeCommentVote('comment-1', { user: mockUser });

      expect(result).toEqual(expectedStats);
      expect(service.removeCommentVote).toHaveBeenCalledWith('comment-1', mockUser.id);
    });
  });

  describe('getCommentVoteStats', () => {
    it('should return comment vote stats', async () => {
      const expectedStats: CommentVoteStatsDto = {
        upvotes: 3,
        downvotes: 0,
      };

      service.getCommentVoteStats.mockResolvedValue(expectedStats);

      const result = await controller.getCommentVoteStats('comment-1', { user: mockUser });

      expect(result).toEqual(expectedStats);
      expect(service.getCommentVoteStats).toHaveBeenCalledWith('comment-1', mockUser.id);
    });
  });

  describe('moderateComment', () => {
    it('should moderate a comment', async () => {
      const moderateDto: ModerateCommentDto = {
        status: CommentStatus.Flagged,
        reason: 'Inappropriate content',
      };

      const moderatedComment = { ...mockCommentResponse, status: CommentStatus.Flagged };
      service.moderateComment.mockResolvedValue(moderatedComment);

      const result = await controller.moderateComment('comment-1', moderateDto, { user: mockUser });

      expect(result).toEqual(moderatedComment);
      expect(service.moderateComment).toHaveBeenCalledWith('comment-1', moderateDto, mockUser.id);
    });
  });

  describe('replyToComment', () => {
    it('should reply to a comment', async () => {
      const replyDto: ReplyToCommentDto = {
        content: 'This is a reply',
      };

      service.replyToComment.mockResolvedValue(mockCommentResponse);

      const result = await controller.replyToComment('comment-1', replyDto, { user: mockUser });

      expect(result).toEqual(mockCommentResponse);
      expect(service.replyToComment).toHaveBeenCalledWith('comment-1', replyDto, mockUser.id);
    });
  });
});

describe('PostCommentsController', () => {
  let controller: PostCommentsController;
  let service: jest.Mocked<CommentsService>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'A test user',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'user.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 2,
      commentsToday: 5,
      votesToday: 15,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    lastLoginAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCommentResponse: CommentResponseDto = {
    id: 'comment-1',
    content: 'This is a test comment',
    status: CommentStatus.Published,
    author: {
      id: mockUser.id,
      uname: mockUser.uname,
      profile: mockUser.profile,
      activity: {
        postsCount: 5,
        commentsCount: 10,
        followersCount: 0,
        followingCount: 0,
      },
      createdAt: mockUser.createdAt,
    },
    post: {
      id: 'post-1',
      title: 'Test Post',
      status: 'Published',
      author: {
        id: mockUser.id,
        uname: mockUser.uname,
        profile: mockUser.profile,
        activity: {
          postsCount: 5,
          commentsCount: 10,
          followersCount: 0,
          followingCount: 0,
        },
        createdAt: mockUser.createdAt,
      },
      community: {
        id: 'community-1',
        name: 'Test Community',
      },
      createdAt: new Date(),
    },
    upvotes: 3,
    downvotes: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockService = {
      getCommentsByPost: jest.fn(),
      getCommentTree: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [PostCommentsController],
      providers: [
        {
          provide: CommentsService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<PostCommentsController>(PostCommentsController);
    service = module.get(CommentsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getCommentsByPost', () => {
    it('should return comments for a post', async () => {
      const query: CommentSearchQueryDto = { page: 1, limit: 20 };
      const expectedResult: CommentListResponseDto = {
        comments: [mockCommentResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getCommentsByPost.mockResolvedValue(expectedResult);

      const result = await controller.getCommentsByPost('post-1', query, { user: mockUser });

      expect(result).toEqual(expectedResult);
      expect(service.getCommentsByPost).toHaveBeenCalledWith('post-1', query, mockUser.id);
    });

    it('should work without authenticated user', async () => {
      const query: CommentSearchQueryDto = { page: 1, limit: 20 };
      const expectedResult: CommentListResponseDto = {
        comments: [mockCommentResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getCommentsByPost.mockResolvedValue(expectedResult);

      const result = await controller.getCommentsByPost('post-1', query, {});

      expect(result).toEqual(expectedResult);
      expect(service.getCommentsByPost).toHaveBeenCalledWith('post-1', query, undefined);
    });
  });

  describe('getCommentTree', () => {
    it('should return comment tree for a post', async () => {
      const expectedResult: CommentTreeResponseDto = {
        comments: [mockCommentResponse],
        totalComments: 1,
        maxDepth: 0,
      };

      service.getCommentTree.mockResolvedValue(expectedResult);

      const result = await controller.getCommentTree('post-1', { user: mockUser });

      expect(result).toEqual(expectedResult);
      expect(service.getCommentTree).toHaveBeenCalledWith('post-1', mockUser.id);
    });
  });
});

describe('AuthorCommentsController', () => {
  let controller: AuthorCommentsController;
  let service: jest.Mocked<CommentsService>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'A test user',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'user.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 2,
      commentsToday: 5,
      votesToday: 15,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    lastLoginAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCommentResponse: CommentResponseDto = {
    id: 'comment-1',
    content: 'This is a test comment',
    status: CommentStatus.Published,
    author: {
      id: mockUser.id,
      uname: mockUser.uname,
      profile: mockUser.profile,
      activity: {
        postsCount: 5,
        commentsCount: 10,
        followersCount: 0,
        followingCount: 0,
      },
      createdAt: mockUser.createdAt,
    },
    post: {
      id: 'post-1',
      title: 'Test Post',
      status: 'Published',
      author: {
        id: mockUser.id,
        uname: mockUser.uname,
        profile: mockUser.profile,
        activity: {
          postsCount: 5,
          commentsCount: 10,
          followersCount: 0,
          followingCount: 0,
        },
        createdAt: mockUser.createdAt,
      },
      community: {
        id: 'community-1',
        name: 'Test Community',
      },
      createdAt: new Date(),
    },
    upvotes: 3,
    downvotes: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockService = {
      getCommentsByAuthor: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthorCommentsController],
      providers: [
        {
          provide: CommentsService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<AuthorCommentsController>(AuthorCommentsController);
    service = module.get(CommentsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getCommentsByAuthor', () => {
    it('should return comments by author', async () => {
      const query: PaginationQueryDto = { page: 1, limit: 20 };
      const expectedResult: CommentListResponseDto = {
        comments: [mockCommentResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getCommentsByAuthor.mockResolvedValue(expectedResult);

      const result = await controller.getCommentsByAuthor('user-1', query, { user: mockUser });

      expect(result).toEqual(expectedResult);
      expect(service.getCommentsByAuthor).toHaveBeenCalledWith('user-1', query, mockUser.id);
    });

    it('should work without authenticated user', async () => {
      const query: PaginationQueryDto = { page: 1, limit: 20 };
      const expectedResult: CommentListResponseDto = {
        comments: [mockCommentResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getCommentsByAuthor.mockResolvedValue(expectedResult);

      const result = await controller.getCommentsByAuthor('user-1', query, {});

      expect(result).toEqual(expectedResult);
      expect(service.getCommentsByAuthor).toHaveBeenCalledWith('user-1', query, undefined);
    });
  });
});
