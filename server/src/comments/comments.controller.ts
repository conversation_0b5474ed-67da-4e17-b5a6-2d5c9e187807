import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '@/auth/guards/jwt.guard';
import { CsrfGuard } from '@/security/guards/csrf.guard';
import { Public } from '@/common/decorators/public.decorator';
import { SkipCsrf } from '@/security/decorators/skip-csrf.decorator';
import { CommentsService } from './comments.service';
import { User } from '@/types/global';
import {
  CreateCommentDto,
  UpdateCommentDto,
  VoteCommentDto,
  CommentSearchQueryDto,
  ReplyToCommentDto,
  CommentResponseDto,
  CommentListResponseDto,
  CommentVoteStatsDto,
  CommentTreeResponseDto,
  PaginationQueryDto
} from './dto/comments.dto';

@Controller('v1/comments')
export class CommentsController {
  constructor(private readonly commentsService: CommentsService) { }

  // Comment CRUD endpoints
  @Post()
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async createComment (
    @Body(ValidationPipe) createDto: CreateCommentDto,
    @Request() req: { user: User },
  ): Promise<CommentResponseDto> {
    return this.commentsService.createComment(createDto, req.user.id);
  }

  @Get(':id')
  @Public()
  @SkipCsrf()
  async getComment (
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user?: User },
  ): Promise<CommentResponseDto> {
    return this.commentsService.getComment(id, req.user?.id);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async updateComment (
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateCommentDto,
    @Request() req: { user: User },
  ): Promise<CommentResponseDto> {
    return this.commentsService.updateComment(id, updateDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteComment (
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: User },
  ): Promise<void> {
    return this.commentsService.deleteComment(id, req.user.id);
  }

  // Comment voting endpoints
  @Post(':id/vote')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async voteOnComment (
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) voteDto: VoteCommentDto,
    @Request() req: { user: User },
  ): Promise<CommentVoteStatsDto> {
    return this.commentsService.voteOnComment(id, voteDto, req.user.id);
  }

  @Delete(':id/vote')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async removeCommentVote (
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: User },
  ): Promise<CommentVoteStatsDto> {
    return this.commentsService.removeCommentVote(id, req.user.id);
  }

  @Get(':id/votes')
  @Public()
  @SkipCsrf()
  async getCommentVoteStats (
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user?: User },
  ): Promise<CommentVoteStatsDto> {
    return this.commentsService.getCommentVoteStats(id, req.user?.id);
  }

  // Comment moderation endpoints
  @Post(':id/moderate')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async moderateComment (
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) moderateDto: ModerateCommentDto,
    @Request() req: { user: User },
  ): Promise<CommentResponseDto> {
    return this.commentsService.moderateComment(id, moderateDto, req.user.id);
  }

  // Reply to comment endpoint
  @Post(':id/reply')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async replyToComment (
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) replyDto: ReplyToCommentDto,
    @Request() req: { user: User },
  ): Promise<CommentResponseDto> {
    return this.commentsService.replyToComment(id, replyDto, req.user.id);
  }
}

// Post-specific comment endpoints (separate controller for better organization)
@Controller('v1/posts/:postId/comments')
export class PostCommentsController {
  constructor(private readonly commentsService: CommentsService) { }

  @Get()
  @Public()
  @SkipCsrf()
  async getCommentsByPost (
    @Param('postId', ParseUUIDPipe) postId: string,
    @Query(ValidationPipe) query: CommentSearchQueryDto,
    @Request() req: { user?: User },
  ): Promise<CommentListResponseDto> {
    return this.commentsService.getCommentsByPost(postId, query, req.user?.id);
  }

  @Get('tree')
  @Public()
  @SkipCsrf()
  async getCommentTree (
    @Param('postId', ParseUUIDPipe) postId: string,
    @Request() req: { user?: User },
  ): Promise<CommentTreeResponseDto> {
    return this.commentsService.getCommentTree(postId, req.user?.id);
  }
}

// Author-specific comment endpoints
@Controller('v1/users/:authorId/comments')
export class AuthorCommentsController {
  constructor(private readonly commentsService: CommentsService) { }

  @Get()
  @Public()
  @SkipCsrf()
  async getCommentsByAuthor (
    @Param('authorId', ParseUUIDPipe) authorId: string,
    @Query(ValidationPipe) query: PaginationQueryDto,
    @Request() req: { user?: User },
  ): Promise<CommentListResponseDto> {
    return this.commentsService.getCommentsByAuthor(authorId, query, req.user?.id);
  }
}
