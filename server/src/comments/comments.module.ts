import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  CommentsController,
  PostCommentsController,
  AuthorCommentsController
} from './comments.controller';
import { CommentsService } from './comments.service';
import {
  CommentRepository,
  PostRepository,
  CommunityRepository,
  UserRepository
} from '@/database/repositories';
import { SecurityModule } from '@/security/security.module';
import {
  CommentEntity,
  CommentVoteEntity,
  PostEntity,
  UserEntity,
  CommunityEntity,
  CommunityMemberEntity
} from '@/database/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CommentEntity,
      CommentVoteEntity,
      PostEntity,
      UserEntity,
      CommunityEntity,
      CommunityMemberEntity,
    ]),
    SecurityModule,
  ],
  controllers: [
    CommentsController,
    PostCommentsController,
    AuthorCommentsController,
  ],
  providers: [
    CommentsService,
    CommentRepository,
    PostRepository,
    CommunityRepository,
    UserRepository,
  ],
  exports: [CommentsService, CommentRepository],
})
export class CommentsModule { }
