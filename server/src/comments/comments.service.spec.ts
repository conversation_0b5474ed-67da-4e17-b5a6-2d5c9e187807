import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { CommentsService } from './comments.service';
import { CommentRepository } from '@/database/repositories/comment.repository';
import { PostRepository, CommunityRepository, UserRepository } from '@/database/repositories';
import {
  Comment,
  User,
  Post,
  Community,
  CommentStatus,
  PostStatus,
  VoteType,
  MemberRole,
  CommunityVisibility,
  InvitePermission,
  PostModeration,
  UserStatus
} from '@/types/global';

describe('CommentsService', () => {
  let service: CommentsService;
  let commentRepository: jest.Mocked<CommentRepository>;
  let postRepository: jest.Mocked<PostRepository>;
  let communityRepository: jest.Mocked<CommunityRepository>;
  let userRepository: jest.Mocked<UserRepository>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'A test user',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'user.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 2,
      commentsToday: 5,
      votesToday: 15,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    lastLoginAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCommunity: Community = {
    id: 'community-1',
    name: 'Test Community',
    description: 'A test community for fishing',
    rules: 'Be respectful. No spam.',
    visibility: CommunityVisibility.Public,
    invitePermission: InvitePermission.Members,
    postModeration: PostModeration.None,
    commentModeration: PostModeration.None,
    pic: 'community.jpg',
    banner: 'banner.jpg',
    owner: mockUser,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPost: Post = {
    id: 'post-1',
    title: 'Test Post',
    status: PostStatus.Published,
    content: 'This is a test post about fishing',
    author: mockUser,
    community: mockCommunity,
    fishingSpot: undefined,
    comments: [],
    tags: [],
    moderation: undefined,
    upvotes: 5,
    downvotes: 1,
    views: 100,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockComment: Comment = {
    id: 'comment-1',
    content: 'This is a test comment',
    status: CommentStatus.Published,
    author: mockUser,
    post: mockPost,
    parentComment: undefined,
    moderation: undefined,
    upvotes: 3,
    downvotes: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockCommentRepository = {
      createComment: jest.fn(),
      findCommentById: jest.fn(),
      updateComment: jest.fn(),
      deleteComment: jest.fn(),
      findCommentsByPost: jest.fn(),
      findCommentsByAuthor: jest.fn(),
      findCommentTree: jest.fn(),
      findCommentReplies: jest.fn(),
      voteOnComment: jest.fn(),
      removeVote: jest.fn(),
      getUserVote: jest.fn(),
      getCommentVoteStats: jest.fn(),
      moderateComment: jest.fn(),
      updateCommentStatus: jest.fn(),
    };

    const mockPostRepository = {
      findPostById: jest.fn(),
    };

    const mockCommunityRepository = {
      findCommunityById: jest.fn(),
      isUserMember: jest.fn(),
      getUserMembership: jest.fn(),
    };

    const mockUserRepository = {
      findUserById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CommentsService,
        {
          provide: CommentRepository,
          useValue: mockCommentRepository,
        },
        {
          provide: PostRepository,
          useValue: mockPostRepository,
        },
        {
          provide: CommunityRepository,
          useValue: mockCommunityRepository,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<CommentsService>(CommentsService);
    commentRepository = module.get(CommentRepository);
    postRepository = module.get(PostRepository);
    communityRepository = module.get(CommunityRepository);
    userRepository = module.get(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createComment', () => {
    it('should create a comment successfully', async () => {
      const createDto = {
        content: 'This is a new comment',
        postId: mockPost.id,
      };

      postRepository.findPostById.mockResolvedValue(mockPost);
      communityRepository.isUserMember.mockResolvedValue(true);
      commentRepository.createComment.mockResolvedValue(mockComment);

      const result = await service.createComment(createDto, mockUser.id);

      expect(result).toBeDefined();
      expect(result.content).toBe(mockComment.content);
      expect(commentRepository.createComment).toHaveBeenCalledWith({
        content: createDto.content,
        status: CommentStatus.Published,
        authorId: mockUser.id,
        postId: createDto.postId,
        parentCommentId: undefined,
      });
    });

    it('should create a reply comment successfully', async () => {
      const parentComment = { ...mockComment, id: 'parent-comment-1' };
      const createDto = {
        content: 'This is a reply',
        postId: mockPost.id,
        parentCommentId: parentComment.id,
      };

      postRepository.findPostById.mockResolvedValue(mockPost);
      communityRepository.isUserMember.mockResolvedValue(true);
      commentRepository.findCommentById.mockResolvedValue(parentComment);
      commentRepository.createComment.mockResolvedValue(mockComment);

      const result = await service.createComment(createDto, mockUser.id);

      expect(result).toBeDefined();
      expect(commentRepository.createComment).toHaveBeenCalledWith({
        content: createDto.content,
        status: CommentStatus.Published,
        authorId: mockUser.id,
        postId: createDto.postId,
        parentCommentId: createDto.parentCommentId,
      });
    });

    it('should throw ForbiddenException for private community non-member', async () => {
      const privateCommunity = { ...mockCommunity, visibility: CommunityVisibility.Private };
      const privatePost = { ...mockPost, community: privateCommunity };
      const createDto = {
        content: 'This is a new comment',
        postId: privatePost.id,
      };

      postRepository.findPostById.mockResolvedValue(privatePost);
      communityRepository.isUserMember.mockResolvedValue(false);

      await expect(service.createComment(createDto, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });

    it('should throw BadRequestException for invalid parent comment', async () => {
      const createDto = {
        content: 'This is a reply',
        postId: mockPost.id,
        parentCommentId: 'invalid-parent-id',
      };

      postRepository.findPostById.mockResolvedValue(mockPost);
      communityRepository.isUserMember.mockResolvedValue(true);
      commentRepository.findCommentById.mockResolvedValue(null);

      await expect(service.createComment(createDto, mockUser.id)).rejects.toThrow(
        BadRequestException
      );
    });
  });

  describe('getComment', () => {
    it('should return a comment successfully', async () => {
      commentRepository.findCommentById.mockResolvedValue(mockComment);

      const result = await service.getComment(mockComment.id, mockUser.id);

      expect(result).toBeDefined();
      expect(result.id).toBe(mockComment.id);
    });

    it('should throw NotFoundException if comment does not exist', async () => {
      commentRepository.findCommentById.mockResolvedValue(null);

      await expect(service.getComment('non-existent-id', mockUser.id)).rejects.toThrow(
        NotFoundException
      );
    });

    it('should throw ForbiddenException for private community non-member', async () => {
      const privateCommunity = { ...mockCommunity, visibility: CommunityVisibility.Private };
      const privatePost = { ...mockPost, community: privateCommunity };
      const privateComment = { ...mockComment, post: privatePost };

      commentRepository.findCommentById.mockResolvedValue(privateComment);
      communityRepository.isUserMember.mockResolvedValue(false);

      await expect(service.getComment(privateComment.id, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('updateComment', () => {
    it('should update a comment successfully by author', async () => {
      const updateDto = {
        content: 'Updated comment content',
      };

      commentRepository.findCommentById.mockResolvedValue(mockComment);
      commentRepository.updateComment.mockResolvedValue({ ...mockComment, ...updateDto });

      const result = await service.updateComment(mockComment.id, updateDto, mockUser.id);

      expect(result).toBeDefined();
      expect(result.content).toBe(updateDto.content);
      expect(commentRepository.updateComment).toHaveBeenCalledWith(mockComment.id, {
        content: updateDto.content,
        status: undefined,
      });
    });

    it('should throw NotFoundException if comment does not exist', async () => {
      const updateDto = { content: 'Updated content' };
      commentRepository.findCommentById.mockResolvedValue(null);

      await expect(service.updateComment('non-existent-id', updateDto, mockUser.id)).rejects.toThrow(
        NotFoundException
      );
    });

    it('should throw ForbiddenException if user is not author or moderator', async () => {
      const updateDto = { content: 'Updated content' };
      commentRepository.findCommentById.mockResolvedValue(mockComment);
      communityRepository.getUserMembership.mockResolvedValue(null);

      await expect(service.updateComment(mockComment.id, updateDto, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('deleteComment', () => {
    it('should delete a comment successfully by author', async () => {
      commentRepository.findCommentById.mockResolvedValue(mockComment);
      commentRepository.deleteComment.mockResolvedValue(undefined);

      await service.deleteComment(mockComment.id, mockUser.id);

      expect(commentRepository.deleteComment).toHaveBeenCalledWith(mockComment.id);
    });

    it('should throw NotFoundException if comment does not exist', async () => {
      commentRepository.findCommentById.mockResolvedValue(null);

      await expect(service.deleteComment('non-existent-id', mockUser.id)).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe('getCommentsByPost', () => {
    it('should return comments for a post successfully', async () => {
      const query = { page: 1, limit: 20 };
      const mockResult = { comments: [mockComment], total: 1 };

      postRepository.findPostById.mockResolvedValue(mockPost);
      commentRepository.findCommentsByPost.mockResolvedValue(mockResult);

      const result = await service.getCommentsByPost(mockPost.id, query);

      expect(result).toBeDefined();
      expect(result.comments).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.totalPages).toBe(1);
    });

    it('should throw NotFoundException if post does not exist', async () => {
      const query = { page: 1, limit: 20 };
      postRepository.findPostById.mockResolvedValue(null);

      await expect(service.getCommentsByPost('non-existent-id', query)).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe('voteOnComment', () => {
    it('should vote on comment successfully', async () => {
      const voteDto = { voteType: VoteType.Upvote };
      const mockStats = { upvotes: 4, downvotes: 0 };

      commentRepository.findCommentById.mockResolvedValue(mockComment);
      commentRepository.voteOnComment.mockResolvedValue(undefined);
      commentRepository.getCommentVoteStats.mockResolvedValue(mockStats);
      commentRepository.getUserVote.mockResolvedValue(VoteType.Upvote);

      const result = await service.voteOnComment(mockComment.id, voteDto, 'other-user-id');

      expect(result).toBeDefined();
      expect(result.upvotes).toBe(4);
      expect(result.userVote).toBe(VoteType.Upvote);
      expect(commentRepository.voteOnComment).toHaveBeenCalledWith(
        'other-user-id',
        mockComment.id,
        VoteType.Upvote
      );
    });

    it('should throw BadRequestException for self-voting', async () => {
      const voteDto = { voteType: VoteType.Upvote };
      commentRepository.findCommentById.mockResolvedValue(mockComment);

      await expect(service.voteOnComment(mockComment.id, voteDto, mockUser.id)).rejects.toThrow(
        BadRequestException
      );
    });

    it('should throw NotFoundException if comment does not exist', async () => {
      const voteDto = { voteType: VoteType.Upvote };
      commentRepository.findCommentById.mockResolvedValue(null);

      await expect(service.voteOnComment('non-existent-id', voteDto, 'other-user-id')).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe('removeCommentVote', () => {
    it('should remove vote successfully', async () => {
      const mockStats = { upvotes: 2, downvotes: 0 };

      commentRepository.findCommentById.mockResolvedValue(mockComment);
      commentRepository.removeVote.mockResolvedValue(undefined);
      commentRepository.getCommentVoteStats.mockResolvedValue(mockStats);

      const result = await service.removeCommentVote(mockComment.id, 'other-user-id');

      expect(result).toBeDefined();
      expect(result.upvotes).toBe(2);
      expect(commentRepository.removeVote).toHaveBeenCalledWith('other-user-id', mockComment.id);
    });
  });

  describe('moderateComment', () => {
    it('should moderate comment successfully by community owner', async () => {
      const moderateDto = { status: CommentStatus.Flagged, reason: 'Spam content' };
      const moderatedComment = { ...mockComment, status: CommentStatus.Flagged };

      commentRepository.findCommentById.mockResolvedValue(mockComment);
      commentRepository.moderateComment.mockResolvedValue(moderatedComment);
      commentRepository.updateCommentStatus.mockResolvedValue(moderatedComment);

      const result = await service.moderateComment(mockComment.id, moderateDto, mockUser.id);

      expect(result).toBeDefined();
      expect(result.status).toBe(CommentStatus.Flagged);
      expect(commentRepository.updateCommentStatus).toHaveBeenCalledWith(mockComment.id, CommentStatus.Flagged);
    });

    it('should throw ForbiddenException if user is not moderator', async () => {
      const moderateDto = { status: CommentStatus.Flagged, reason: 'Spam content' };
      commentRepository.findCommentById.mockResolvedValue(mockComment);
      communityRepository.getUserMembership.mockResolvedValue(null);

      await expect(service.moderateComment(mockComment.id, moderateDto, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('replyToComment', () => {
    it('should create a reply successfully', async () => {
      const replyDto = { content: 'This is a reply' };
      const parentComment = mockComment;

      commentRepository.findCommentById.mockResolvedValue(parentComment);
      postRepository.findPostById.mockResolvedValue(mockPost);
      communityRepository.isUserMember.mockResolvedValue(true);
      commentRepository.createComment.mockResolvedValue(mockComment);

      const result = await service.replyToComment(parentComment.id, replyDto, mockUser.id);

      expect(result).toBeDefined();
      expect(commentRepository.createComment).toHaveBeenCalledWith({
        content: replyDto.content,
        status: CommentStatus.Published,
        authorId: mockUser.id,
        postId: parentComment.post.id,
        parentCommentId: parentComment.id,
      });
    });

    it('should throw NotFoundException if parent comment does not exist', async () => {
      const replyDto = { content: 'This is a reply' };
      commentRepository.findCommentById.mockResolvedValue(null);

      await expect(service.replyToComment('non-existent-id', replyDto, mockUser.id)).rejects.toThrow(
        NotFoundException
      );
    });
  });
});
