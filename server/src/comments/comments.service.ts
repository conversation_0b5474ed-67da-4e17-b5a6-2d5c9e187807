import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  ConflictException
} from '@nestjs/common';
import { CommentRepository } from '@/database/repositories/comment.repository';
import { PostRepository, CommunityRepository, UserRepository } from '@/database/repositories';
import { AppConfigService } from '@/config/config.service';
import {
  Comment,
  User,
  Post,
  CommentStatus,
  VoteType,
  MemberRole,
  CommunityVisibility
} from '@/types/global';
import {
  CreateCommentDto,
  UpdateCommentDto,
  VoteCommentDto,
  CommentSearchQueryDto,
  ModerateCommentDto,
  ReplyToCommentDto,
  CommentResponseDto,
  CommentListResponseDto,
  CommentVoteStatsDto,
  CommentTreeResponseDto,
  PublicUserProfileDto,
  PostBasicDto,
  PaginationQueryDto
} from './dto/comments.dto';

@Injectable()
export class CommentsService {
  private readonly logger = new Logger(CommentsService.name);

  constructor(
    private readonly commentRepository: CommentRepository,
    private readonly postRepository: PostRepository,
    private readonly communityRepository: CommunityRepository,
    private readonly userRepository: UserRepository,
    private readonly configService: AppConfigService,
  ) { }

  // Helper methods for converting entities to response DTOs
  private userToPublicProfile (user: User): PublicUserProfileDto {
    return {
      id: user.id,
      uname: user.uname,
      profile: {
        displayName: user.profile.displayName,
        bio: user.profile.bio,
        location: user.profile.location,
        website: user.profile.website,
        pic: user.profile.pic,
        banner: user.profile.banner,
      },
      activity: {
        postsCount: user.activity?.postsToday || 0,
        commentsCount: user.activity?.commentsToday || 0,
        followersCount: 0, // TODO: Calculate from relationships
        followingCount: 0, // TODO: Calculate from relationships
      },
      createdAt: user.createdAt,
    };
  }

  private postToBasic (post: Post): PostBasicDto {
    return {
      id: post.id,
      title: post.title,
      status: post.status,
      author: this.userToPublicProfile(post.author),
      community: {
        id: post.community?.id || '',
        name: post.community?.name || '',
      },
      createdAt: post.createdAt,
    };
  }

  private async commentToResponse (comment: Comment, currentUserId?: string): Promise<CommentResponseDto> {
    let userVote: VoteType | undefined;

    if (currentUserId) {
      userVote = await this.commentRepository.getUserVote(currentUserId, comment.id);
    }

    return {
      id: comment.id,
      content: comment.content,
      status: comment.status,
      author: this.userToPublicProfile(comment.author),
      post: this.postToBasic(comment.post),
      parentComment: comment.parentComment,
      upvotes: comment.upvotes,
      downvotes: comment.downvotes,
      userVote,
      createdAt: comment.createdAt,
      updatedAt: comment.updatedAt,
    };
  }

  // Permission checking methods
  private async checkCommentPermission (postId: string, userId: string): Promise<void> {
    const post = await this.postRepository.findPostById(postId);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Check if user can comment in the community
    if (post.community.visibility === CommunityVisibility.Private) {
      const isMember = await this.communityRepository.isUserMember(post.community.id, userId);
      if (!isMember && post.community.owner.id !== userId) {
        throw new ForbiddenException('You cannot comment on posts in this private community');
      }
    }
  }

  private async checkModifyPermission (comment: Comment, userId: string): Promise<void> {
    // Get user to check if they're a super user
    const user = await this.userRepository.findUserById(userId);
    if (!user) {
      throw new ForbiddenException('User not found');
    }

    // Super users can modify any comment
    if (this.configService.isSuperUser(user.uname)) {
      return;
    }

    // Author can always modify their own comments
    if (comment.author.id === userId) {
      return;
    }

    // Check if user is a moderator/admin in the community
    const membership = await this.communityRepository.getUserMembership(comment.post.community.id, userId);
    if (membership && (membership.role === MemberRole.Moderator || membership.role === MemberRole.Admin)) {
      return;
    }

    // Community owner can moderate
    if (comment.post.community.owner.id === userId) {
      return;
    }

    throw new ForbiddenException('You do not have permission to modify this comment');
  }

  // Comment CRUD operations
  async createComment (createDto: CreateCommentDto, authorId: string): Promise<CommentResponseDto> {
    try {
      // Check if user can comment on this post
      await this.checkCommentPermission(createDto.postId, authorId);

      // If this is a reply, validate parent comment exists and belongs to the same post
      if (createDto.parentCommentId) {
        const parentComment = await this.commentRepository.findCommentById(createDto.parentCommentId);
        if (!parentComment) {
          throw new BadRequestException('Parent comment not found');
        }
        if (parentComment.post.id !== createDto.postId) {
          throw new BadRequestException('Parent comment does not belong to this post');
        }
      }

      // Create the comment
      const comment = await this.commentRepository.createComment({
        content: createDto.content,
        status: createDto.status || CommentStatus.Published,
        authorId,
        postId: createDto.postId,
        parentCommentId: createDto.parentCommentId,
      });

      this.logger.log(`Created comment: ${comment.id} by user ${authorId}`);
      return this.commentToResponse(comment, authorId);
    } catch (error) {
      this.logger.error(`Failed to create comment: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getComment (id: string, currentUserId?: string): Promise<CommentResponseDto> {
    const comment = await this.commentRepository.findCommentById(id);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // Check if user can view the comment (for private communities)
    if (comment.post.community.visibility === CommunityVisibility.Private && currentUserId) {
      const isMember = await this.communityRepository.isUserMember(comment.post.community.id, currentUserId);
      if (!isMember && comment.post.community.owner.id !== currentUserId && comment.author.id !== currentUserId) {
        throw new ForbiddenException('This comment is in a private community');
      }
    }

    return this.commentToResponse(comment, currentUserId);
  }

  async updateComment (
    id: string,
    updateDto: UpdateCommentDto,
    userId: string
  ): Promise<CommentResponseDto> {
    const comment = await this.commentRepository.findCommentById(id);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // Check permissions
    await this.checkModifyPermission(comment, userId);

    const updatedComment = await this.commentRepository.updateComment(id, {
      content: updateDto.content,
      status: updateDto.status,
    });

    this.logger.log(`Updated comment: ${id} by user ${userId}`);
    return this.commentToResponse(updatedComment, userId);
  }

  async deleteComment (id: string, userId: string): Promise<void> {
    const comment = await this.commentRepository.findCommentById(id);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // Check permissions
    await this.checkModifyPermission(comment, userId);

    await this.commentRepository.deleteComment(id);
    this.logger.log(`Deleted comment: ${id} by user ${userId}`);
  }

  // Comment listing and search
  async getCommentsByPost (
    postId: string,
    query: CommentSearchQueryDto,
    currentUserId?: string
  ): Promise<CommentListResponseDto> {
    // Check if post exists and user can view it
    const post = await this.postRepository.findPostById(postId);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    if (post.community.visibility === CommunityVisibility.Private && currentUserId) {
      const isMember = await this.communityRepository.isUserMember(post.community.id, currentUserId);
      if (!isMember && post.community.owner.id !== currentUserId) {
        throw new ForbiddenException('You cannot view comments from this private community');
      }
    }

    const filters: any = {
      status: query.status || CommentStatus.Published,
      search: query.search,
    };

    const { comments, total } = await this.commentRepository.findCommentsByPost(
      postId,
      query.page || 1,
      query.limit || 20,
      filters
    );

    const commentResponses = await Promise.all(
      comments.map(comment => this.commentToResponse(comment, currentUserId))
    );

    const totalPages = Math.ceil(total / (query.limit || 20));

    return {
      comments: commentResponses,
      total,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  async getCommentsByAuthor (
    authorId: string,
    query: PaginationQueryDto,
    currentUserId?: string
  ): Promise<CommentListResponseDto> {
    const { comments, total } = await this.commentRepository.findCommentsByAuthor(
      authorId,
      query.page || 1,
      query.limit || 20
    );

    // Filter out comments from private communities that the current user can't see
    const visibleComments = [];
    for (const comment of comments) {
      if (comment.post.community.visibility === CommunityVisibility.Private && currentUserId) {
        const isMember = await this.communityRepository.isUserMember(comment.post.community.id, currentUserId);
        if (!isMember && comment.post.community.owner.id !== currentUserId && comment.author.id !== currentUserId) {
          continue; // Skip this comment
        }
      }
      visibleComments.push(comment);
    }

    const commentResponses = await Promise.all(
      visibleComments.map(comment => this.commentToResponse(comment, currentUserId))
    );

    const totalPages = Math.ceil(visibleComments.length / (query.limit || 20));

    return {
      comments: commentResponses,
      total: visibleComments.length,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  // Threaded comment operations
  async getCommentTree (postId: string, currentUserId?: string): Promise<CommentTreeResponseDto> {
    // Check if post exists and user can view it
    const post = await this.postRepository.findPostById(postId);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    if (post.community.visibility === CommunityVisibility.Private && currentUserId) {
      const isMember = await this.communityRepository.isUserMember(post.community.id, currentUserId);
      if (!isMember && post.community.owner.id !== currentUserId) {
        throw new ForbiddenException('You cannot view comments from this private community');
      }
    }

    const comments = await this.commentRepository.findCommentTree(postId);
    const commentResponses = await Promise.all(
      comments.map(comment => this.commentToResponse(comment, currentUserId))
    );

    return {
      comments: commentResponses,
      totalComments: comments.length,
      maxDepth: 0, // TODO: Calculate actual max depth from tree structure
    };
  }

  async replyToComment (
    parentCommentId: string,
    replyDto: ReplyToCommentDto,
    authorId: string
  ): Promise<CommentResponseDto> {
    const parentComment = await this.commentRepository.findCommentById(parentCommentId);

    if (!parentComment) {
      throw new NotFoundException('Parent comment not found');
    }

    // Check if user can comment on this post
    await this.checkCommentPermission(parentComment.post.id, authorId);

    // Create the reply
    const comment = await this.commentRepository.createComment({
      content: replyDto.content,
      status: replyDto.status || CommentStatus.Published,
      authorId,
      postId: parentComment.post.id,
      parentCommentId,
    });

    this.logger.log(`Created reply: ${comment.id} to comment ${parentCommentId} by user ${authorId}`);
    return this.commentToResponse(comment, authorId);
  }

  // Comment voting operations
  async voteOnComment (commentId: string, voteDto: VoteCommentDto, userId: string): Promise<CommentVoteStatsDto> {
    const comment = await this.commentRepository.findCommentById(commentId);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // Check if user can vote (must be community member for private communities)
    if (comment.post.community.visibility === CommunityVisibility.Private) {
      const isMember = await this.communityRepository.isUserMember(comment.post.community.id, userId);
      if (!isMember && comment.post.community.owner.id !== userId) {
        throw new ForbiddenException('You cannot vote on comments in this private community');
      }
    }

    // Prevent self-voting
    if (comment.author.id === userId) {
      throw new BadRequestException('You cannot vote on your own comment');
    }

    await this.commentRepository.voteOnComment(userId, commentId, voteDto.voteType);

    // Return updated vote stats
    return this.getCommentVoteStats(commentId, userId);
  }

  async removeCommentVote (commentId: string, userId: string): Promise<CommentVoteStatsDto> {
    const comment = await this.commentRepository.findCommentById(commentId);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    await this.commentRepository.removeVote(userId, commentId);

    // Return updated vote stats
    return this.getCommentVoteStats(commentId, userId);
  }

  async getCommentVoteStats (commentId: string, userId?: string): Promise<CommentVoteStatsDto> {
    const comment = await this.commentRepository.findCommentById(commentId);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    const stats = await this.commentRepository.getCommentVoteStats(commentId);
    let userVote: VoteType | undefined;

    if (userId) {
      userVote = await this.commentRepository.getUserVote(userId, commentId);
    }

    return {
      upvotes: stats.upvotes,
      downvotes: stats.downvotes,
      userVote,
    };
  }

  // Comment moderation operations
  async moderateComment (
    commentId: string,
    moderateDto: ModerateCommentDto,
    moderatorId: string
  ): Promise<CommentResponseDto> {
    const comment = await this.commentRepository.findCommentById(commentId);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // Get moderator user to check if they're a super user
    const moderator = await this.userRepository.findUserById(moderatorId);
    if (!moderator) {
      throw new ForbiddenException('Moderator not found');
    }

    // Super users can moderate any comment
    if (this.configService.isSuperUser(moderator.uname)) {
      // Continue to moderation logic
    } else {
      // Check if user has moderation permissions
      const membership = await this.communityRepository.getUserMembership(comment.post.community.id, moderatorId);
      if (!membership || (membership.role !== MemberRole.Moderator && membership.role !== MemberRole.Admin)) {
        // Check if user is community owner
        if (comment.post.community.owner.id !== moderatorId) {
          throw new ForbiddenException('You do not have permission to moderate this comment');
        }
      }
    }

    // Create moderation data
    const moderationData = {
      moderatedBy: moderatorId,
      moderatedAt: new Date(),
      action: `Status changed to ${moderateDto.status}`,
      logs: [
        {
          id: `mod_${Date.now()}`,
          message: moderateDto.reason || `Comment status changed to ${moderateDto.status}`,
          createdBy: moderatorId,
          createdAt: new Date(),
        },
      ],
    };

    const moderatedComment = await this.commentRepository.moderateComment(commentId, moderationData);
    await this.commentRepository.updateCommentStatus(commentId, moderateDto.status);

    this.logger.log(`Moderated comment: ${commentId} by user ${moderatorId}`);
    return this.commentToResponse(moderatedComment, moderatorId);
  }
}
