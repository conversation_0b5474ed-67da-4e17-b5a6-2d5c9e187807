import { 
  IsString, 
  IsEnum, 
  IsOptional, 
  IsUUID, 
  <PERSON>Int, 
  Min, 
  Max, 
  Length,
  IsNotEmpty,
  ValidateNested,
  IsArray,
  IsBoolean
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { 
  CommentStatus,
  VoteType,
  Comment,
  User,
  Post
} from '@/types/global';

// Comment Creation DTO
export class CreateCommentDto {
  @IsString()
  @IsNotEmpty()
  @Length(1, 5000)
  content: string;

  @IsUUID()
  postId: string;

  @IsUUID()
  @IsOptional()
  parentCommentId?: string;

  @IsEnum(CommentStatus)
  @IsOptional()
  status?: CommentStatus = CommentStatus.Published;
}

// Comment Update DTO
export class UpdateCommentDto {
  @IsString()
  @IsOptional()
  @Length(1, 5000)
  content?: string;

  @IsEnum(CommentStatus)
  @IsOptional()
  status?: CommentStatus;
}

// Comment Vote DTO
export class VoteCommentDto {
  @IsEnum(VoteType)
  voteType: VoteType;
}

// Comment Search Query DTO
export class CommentSearchQueryDto {
  @IsOptional()
  @IsString()
  @Length(1, 100)
  search?: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsUUID()
  postId?: string;

  @IsOptional()
  @IsUUID()
  authorId?: string;

  @IsOptional()
  @IsUUID()
  parentCommentId?: string;

  @IsOptional()
  @IsEnum(CommentStatus)
  status?: CommentStatus;

  @IsOptional()
  @IsString()
  @IsEnum(['newest', 'oldest', 'popular'])
  sortBy?: 'newest' | 'oldest' | 'popular' = 'newest';

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  threaded?: boolean = false;
}

// Response DTOs
export class PublicUserProfileDto {
  id: string;
  uname: string;
  profile: {
    displayName: string;
    bio?: string;
    location?: string;
    website?: string;
    pic?: string;
    banner?: string;
  };
  activity: {
    postsCount: number;
    commentsCount: number;
    followersCount: number;
    followingCount: number;
  };
  createdAt: Date;
}

export class PostBasicDto {
  id: string;
  title: string;
  status: string;
  author: PublicUserProfileDto;
  community: {
    id: string;
    name: string;
  };
  createdAt: Date;
}

export class CommentResponseDto {
  id: string;
  content: string;
  status: CommentStatus;
  author: PublicUserProfileDto;
  post: PostBasicDto;
  parentComment?: string;
  upvotes: number;
  downvotes: number;
  userVote?: VoteType;
  replies?: CommentResponseDto[];
  replyCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

export class CommentListResponseDto {
  comments: CommentResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class CommentVoteStatsDto {
  upvotes: number;
  downvotes: number;
  userVote?: VoteType;
}

// Pagination Query DTO (reusable)
export class PaginationQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

// Comment Moderation DTO
export class ModerateCommentDto {
  @IsEnum(CommentStatus)
  status: CommentStatus;

  @IsString()
  @IsOptional()
  @Length(1, 500)
  reason?: string;
}

// Reply to Comment DTO
export class ReplyToCommentDto {
  @IsString()
  @IsNotEmpty()
  @Length(1, 5000)
  content: string;

  @IsEnum(CommentStatus)
  @IsOptional()
  status?: CommentStatus = CommentStatus.Published;
}

// Comment Tree Response DTO (for threaded comments)
export class CommentTreeResponseDto {
  comments: CommentResponseDto[];
  totalComments: number;
  maxDepth: number;
}
