import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext } from '@nestjs/common';
import { CommunitiesController } from './communities.controller';
import { CommunitiesService } from './communities.service';
import { JwtAuthGuard } from '@/auth/guards/jwt.guard';
import { CsrfGuard } from '@/security/guards/csrf.guard';
import {
  CommunityResponseDto,
  CommunityListResponseDto,
  CreateCommunityDto,
  UpdateCommunityDto,
  CommunityMemberResponseDto,
  CommunityMembersResponseDto,
  UpdateMemberRoleDto,
  CreateCommunityInviteDto,
  CommunityInviteResponseDto,
  CommunityInvitesResponseDto,
  PaginationQueryDto,
  InviteStatusQueryDto,
  CommunitySearchQueryDto
} from './dto/communities.dto';
import { User, MemberRole, CommunityVisibility, UserStatus } from '@/types/global';

describe('CommunitiesController', () => {
  let controller: CommunitiesController;
  let service: jest.Mocked<CommunitiesService>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'test.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 5,
      commentsToday: 10,
      votesToday: 15,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCommunityResponse: CommunityResponseDto = {
    id: 'community-1',
    name: 'Test Community',
    description: 'A test community',
    rules: 'Be respectful',
    visibility: CommunityVisibility.Public,
    invitePermission: 'Moderators' as any,
    postModeration: 'None' as any,
    commentModeration: 'None' as any,
    pic: 'community.jpg',
    banner: 'banner.jpg',
    owner: {
      id: mockUser.id,
      uname: mockUser.uname,
      profile: mockUser.profile,
      activity: mockUser.activity,
      createdAt: mockUser.createdAt,
    },
    memberCount: 1,
    isUserMember: true,
    userRole: MemberRole.Admin,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockMemberResponse: CommunityMemberResponseDto = {
    id: 'member-1',
    user: {
      id: mockUser.id,
      uname: mockUser.uname,
      profile: mockUser.profile,
      activity: mockUser.activity,
      createdAt: mockUser.createdAt,
    },
    role: MemberRole.Member,
    joinedAt: new Date(),
  };

  beforeEach(async () => {
    const mockService = {
      createCommunity: jest.fn(),
      getCommunity: jest.fn(),
      updateCommunity: jest.fn(),
      deleteCommunity: jest.fn(),
      getPublicCommunities: jest.fn(),
      joinCommunity: jest.fn(),
      leaveCommunity: jest.fn(),
      getCommunityMembers: jest.fn(),
      updateMemberRole: jest.fn(),
      getUserCommunities: jest.fn(),
      createInvite: jest.fn(),
      getCommunityInvites: jest.fn(),
      getUserInvites: jest.fn(),
      respondToInvite: jest.fn(),
      deleteInvite: jest.fn(),
    };

    // Mock guards to avoid dependency injection issues
    const mockJwtAuthGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const mockCsrfGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommunitiesController],
      providers: [
        {
          provide: CommunitiesService,
          useValue: mockService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(CsrfGuard)
      .useValue(mockCsrfGuard)
      .compile();

    controller = module.get<CommunitiesController>(CommunitiesController);
    service = module.get(CommunitiesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getPublicCommunities', () => {
    it('should return public communities', async () => {
      const query: CommunitySearchQueryDto = { page: 1, limit: 20 };
      const expectedResult: CommunityListResponseDto = {
        communities: [mockCommunityResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getPublicCommunities.mockResolvedValue(expectedResult);

      const result = await controller.getPublicCommunities(query);

      expect(result).toEqual(expectedResult);
      expect(service.getPublicCommunities).toHaveBeenCalledWith(query);
    });
  });

  describe('createCommunity', () => {
    it('should create a community', async () => {
      const createDto: CreateCommunityDto = {
        name: 'New Community',
        description: 'A new fishing community',
        rules: 'Follow the rules',
      };
      const req = { user: mockUser };

      service.createCommunity.mockResolvedValue(mockCommunityResponse);

      const result = await controller.createCommunity(createDto, req);

      expect(result).toEqual(mockCommunityResponse);
      expect(service.createCommunity).toHaveBeenCalledWith(createDto, mockUser.id);
    });
  });

  describe('getCommunity', () => {
    it('should return a community', async () => {
      const communityId = 'community-1';
      const req = { user: mockUser };

      service.getCommunity.mockResolvedValue(mockCommunityResponse);

      const result = await controller.getCommunity(communityId, req);

      expect(result).toEqual(mockCommunityResponse);
      expect(service.getCommunity).toHaveBeenCalledWith(communityId, mockUser.id);
    });

    it('should work without authenticated user', async () => {
      const communityId = 'community-1';
      const req = {};

      service.getCommunity.mockResolvedValue(mockCommunityResponse);

      const result = await controller.getCommunity(communityId, req);

      expect(result).toEqual(mockCommunityResponse);
      expect(service.getCommunity).toHaveBeenCalledWith(communityId, undefined);
    });
  });

  describe('updateCommunity', () => {
    it('should update a community', async () => {
      const communityId = 'community-1';
      const updateDto: UpdateCommunityDto = {
        description: 'Updated description',
      };
      const req = { user: mockUser };

      service.updateCommunity.mockResolvedValue(mockCommunityResponse);

      const result = await controller.updateCommunity(communityId, updateDto, req);

      expect(result).toEqual(mockCommunityResponse);
      expect(service.updateCommunity).toHaveBeenCalledWith(communityId, updateDto, mockUser.id);
    });
  });

  describe('deleteCommunity', () => {
    it('should delete a community', async () => {
      const communityId = 'community-1';
      const req = { user: mockUser };

      service.deleteCommunity.mockResolvedValue(undefined);

      await controller.deleteCommunity(communityId, req);

      expect(service.deleteCommunity).toHaveBeenCalledWith(communityId, mockUser.id);
    });
  });

  describe('joinCommunity', () => {
    it('should allow user to join community', async () => {
      const communityId = 'community-1';
      const req = { user: mockUser };

      service.joinCommunity.mockResolvedValue(mockMemberResponse);

      const result = await controller.joinCommunity(communityId, req);

      expect(result).toEqual(mockMemberResponse);
      expect(service.joinCommunity).toHaveBeenCalledWith(communityId, mockUser.id);
    });
  });

  describe('leaveCommunity', () => {
    it('should allow user to leave community', async () => {
      const communityId = 'community-1';
      const req = { user: mockUser };

      service.leaveCommunity.mockResolvedValue(undefined);

      await controller.leaveCommunity(communityId, req);

      expect(service.leaveCommunity).toHaveBeenCalledWith(communityId, mockUser.id);
    });
  });

  describe('getCommunityMembers', () => {
    it('should return community members', async () => {
      const communityId = 'community-1';
      const query: PaginationQueryDto = { page: 1, limit: 20 };
      const req = { user: mockUser };
      const expectedResult: CommunityMembersResponseDto = {
        members: [mockMemberResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getCommunityMembers.mockResolvedValue(expectedResult);

      const result = await controller.getCommunityMembers(communityId, query, req);

      expect(result).toEqual(expectedResult);
      expect(service.getCommunityMembers).toHaveBeenCalledWith(communityId, query, mockUser.id);
    });
  });

  describe('updateMemberRole', () => {
    it('should update member role', async () => {
      const communityId = 'community-1';
      const userId = 'user-2';
      const updateDto: UpdateMemberRoleDto = { role: MemberRole.Moderator };
      const req = { user: mockUser };

      service.updateMemberRole.mockResolvedValue({
        ...mockMemberResponse,
        role: MemberRole.Moderator,
      });

      const result = await controller.updateMemberRole(communityId, userId, updateDto, req);

      expect(result.role).toBe(MemberRole.Moderator);
      expect(service.updateMemberRole).toHaveBeenCalledWith(
        communityId,
        userId,
        updateDto,
        mockUser.id
      );
    });
  });

  describe('createInvite', () => {
    it('should create an invite', async () => {
      const communityId = 'community-1';
      const createDto: CreateCommunityInviteDto = {
        invitedUserId: 'user-2',
        role: MemberRole.Member,
      };
      const req = { user: mockUser };
      const mockInviteResponse: CommunityInviteResponseDto = {
        id: 'invite-1',
        community: {
          id: communityId,
          name: 'Test Community',
          description: 'A test community',
          pic: 'community.jpg',
        },
        invitedBy: {
          id: mockUser.id,
          uname: mockUser.uname,
          profile: mockUser.profile,
          activity: mockUser.activity,
          createdAt: mockUser.createdAt,
        },
        invitedUser: {
          id: 'user-2',
          uname: 'inviteduser',
          profile: mockUser.profile,
          activity: mockUser.activity,
          createdAt: new Date(),
        },
        role: MemberRole.Member,
        status: 'Pending',
        createdAt: new Date(),
      };

      service.createInvite.mockResolvedValue(mockInviteResponse);

      const result = await controller.createInvite(communityId, createDto, req);

      expect(result).toEqual(mockInviteResponse);
      expect(service.createInvite).toHaveBeenCalledWith(communityId, createDto, mockUser.id);
    });
  });

  describe('acceptInvite', () => {
    it('should accept an invite', async () => {
      const inviteId = 'invite-1';
      const req = { user: mockUser };
      const mockInviteResponse: CommunityInviteResponseDto = {
        id: inviteId,
        community: {
          id: 'community-1',
          name: 'Test Community',
          description: 'A test community',
          pic: 'community.jpg',
        },
        invitedBy: {
          id: 'user-2',
          uname: 'inviter',
          profile: mockUser.profile,
          activity: mockUser.activity,
          createdAt: new Date(),
        },
        invitedUser: {
          id: mockUser.id,
          uname: mockUser.uname,
          profile: mockUser.profile,
          activity: mockUser.activity,
          createdAt: mockUser.createdAt,
        },
        role: MemberRole.Member,
        status: 'Accepted',
        createdAt: new Date(),
      };

      service.respondToInvite.mockResolvedValue(mockInviteResponse);

      const result = await controller.acceptInvite(inviteId, req);

      expect(result).toEqual(mockInviteResponse);
      expect(service.respondToInvite).toHaveBeenCalledWith(
        inviteId,
        { status: 'Accepted' },
        mockUser.id
      );
    });
  });

  describe('rejectInvite', () => {
    it('should reject an invite', async () => {
      const inviteId = 'invite-1';
      const req = { user: mockUser };
      const mockInviteResponse: CommunityInviteResponseDto = {
        id: inviteId,
        community: {
          id: 'community-1',
          name: 'Test Community',
          description: 'A test community',
          pic: 'community.jpg',
        },
        invitedBy: {
          id: 'user-2',
          uname: 'inviter',
          profile: mockUser.profile,
          activity: mockUser.activity,
          createdAt: new Date(),
        },
        invitedUser: {
          id: mockUser.id,
          uname: mockUser.uname,
          profile: mockUser.profile,
          activity: mockUser.activity,
          createdAt: mockUser.createdAt,
        },
        role: MemberRole.Member,
        status: 'Rejected',
        createdAt: new Date(),
      };

      service.respondToInvite.mockResolvedValue(mockInviteResponse);

      const result = await controller.rejectInvite(inviteId, req);

      expect(result).toEqual(mockInviteResponse);
      expect(service.respondToInvite).toHaveBeenCalledWith(
        inviteId,
        { status: 'Rejected' },
        mockUser.id
      );
    });
  });
});
