import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '@/auth/guards/jwt.guard';
import { CsrfGuard } from '@/security/guards/csrf.guard';
import { Public } from '@/common/decorators/public.decorator';
import { SkipCsrf } from '@/security/decorators/skip-csrf.decorator';
import { CommunitiesService } from './communities.service';
import { User } from '@/types/global';
import {
  CreateCommunityDto,
  UpdateCommunityDto,
  CommunitySearchQueryDto,
  UpdateMemberRoleDto,
  CreateCommunityInviteDto,
  UpdateInviteStatusDto,
  CommunityResponseDto,
  CommunityListResponseDto,
  CommunityMemberResponseDto,
  CommunityMembersResponseDto,
  CommunityInviteResponseDto,
  CommunityInvitesResponseDto,
  PaginationQueryDto,
  InviteStatusQueryDto,
} from './dto/communities.dto';

@Controller('v1/communities')
export class CommunitiesController {
  constructor(private readonly communitiesService: CommunitiesService) {}

  // Community CRUD endpoints
  @Get()
  @Public()
  @SkipCsrf()
  async getPublicCommunities(
    @Query(ValidationPipe) query: CommunitySearchQueryDto,
  ): Promise<CommunityListResponseDto> {
    return this.communitiesService.getPublicCommunities(query);
  }

  @Post()
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async createCommunity(
    @Body(ValidationPipe) createDto: CreateCommunityDto,
    @Request() req: { user: User },
  ): Promise<CommunityResponseDto> {
    return this.communitiesService.createCommunity(createDto, req.user.id);
  }

  @Get(':id')
  @Public()
  @SkipCsrf()
  async getCommunity(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user?: User },
  ): Promise<CommunityResponseDto> {
    return this.communitiesService.getCommunity(id, req.user?.id);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async updateCommunity(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateCommunityDto,
    @Request() req: { user: User },
  ): Promise<CommunityResponseDto> {
    return this.communitiesService.updateCommunity(id, updateDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCommunity(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: User },
  ): Promise<void> {
    return this.communitiesService.deleteCommunity(id, req.user.id);
  }

  // Community membership endpoints
  @Post(':id/join')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async joinCommunity(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: User },
  ): Promise<CommunityMemberResponseDto> {
    return this.communitiesService.joinCommunity(id, req.user.id);
  }

  @Delete(':id/leave')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async leaveCommunity(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: User },
  ): Promise<void> {
    return this.communitiesService.leaveCommunity(id, req.user.id);
  }

  @Get(':id/members')
  @Public()
  @SkipCsrf()
  async getCommunityMembers(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) query: PaginationQueryDto,
    @Request() req: { user?: User },
  ): Promise<CommunityMembersResponseDto> {
    return this.communitiesService.getCommunityMembers(id, query, req.user?.id);
  }

  @Put(':id/members/:userId/role')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async updateMemberRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body(ValidationPipe) updateDto: UpdateMemberRoleDto,
    @Request() req: { user: User },
  ): Promise<CommunityMemberResponseDto> {
    return this.communitiesService.updateMemberRole(id, userId, updateDto, req.user.id);
  }

  // Community invitation endpoints
  @Post(':id/invite')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async createInvite(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) createDto: CreateCommunityInviteDto,
    @Request() req: { user: User },
  ): Promise<CommunityInviteResponseDto> {
    return this.communitiesService.createInvite(id, createDto, req.user.id);
  }

  @Get(':id/invites')
  @UseGuards(JwtAuthGuard)
  @SkipCsrf()
  async getCommunityInvites(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) query: InviteStatusQueryDto,
    @Request() req: { user: User },
  ): Promise<CommunityInvitesResponseDto> {
    return this.communitiesService.getCommunityInvites(id, query, req.user.id);
  }

  // User-specific endpoints
  @Get('me/communities')
  @UseGuards(JwtAuthGuard)
  @SkipCsrf()
  async getUserCommunities(
    @Query(ValidationPipe) query: PaginationQueryDto,
    @Request() req: { user: User },
  ): Promise<CommunityListResponseDto> {
    return this.communitiesService.getUserCommunities(req.user.id, query);
  }

  @Get('me/invites')
  @UseGuards(JwtAuthGuard)
  @SkipCsrf()
  async getUserInvites(
    @Query(ValidationPipe) query: InviteStatusQueryDto,
    @Request() req: { user: User },
  ): Promise<CommunityInvitesResponseDto> {
    return this.communitiesService.getUserInvites(req.user.id, query);
  }

  // Invite response endpoints
  @Post('invites/:inviteId/accept')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async acceptInvite(
    @Param('inviteId', ParseUUIDPipe) inviteId: string,
    @Request() req: { user: User },
  ): Promise<CommunityInviteResponseDto> {
    return this.communitiesService.respondToInvite(
      inviteId,
      { status: 'Accepted' },
      req.user.id,
    );
  }

  @Post('invites/:inviteId/reject')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async rejectInvite(
    @Param('inviteId', ParseUUIDPipe) inviteId: string,
    @Request() req: { user: User },
  ): Promise<CommunityInviteResponseDto> {
    return this.communitiesService.respondToInvite(
      inviteId,
      { status: 'Rejected' },
      req.user.id,
    );
  }

  @Delete('invites/:inviteId')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteInvite(
    @Param('inviteId', ParseUUIDPipe) inviteId: string,
    @Request() req: { user: User },
  ): Promise<void> {
    return this.communitiesService.deleteInvite(inviteId, req.user.id);
  }
}
