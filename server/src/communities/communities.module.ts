import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommunitiesController } from './communities.controller';
import { CommunitiesService } from './communities.service';
import { CommunityRepository, UserRepository } from '@/database/repositories';
import { SecurityModule } from '@/security/security.module';
import { ConfigModule } from '@/config/config.module';
import {
  CommunityEntity,
  CommunityMemberEntity,
  CommunityInviteEntity,
  UserEntity
} from '@/database/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CommunityEntity,
      CommunityMemberEntity,
      CommunityInviteEntity,
      UserEntity,
    ]),
    SecurityModule,
    ConfigModule,
  ],
  controllers: [CommunitiesController],
  providers: [
    CommunitiesService,
    CommunityRepository,
    UserRepository,
  ],
  exports: [CommunitiesService, CommunityRepository],
})
export class CommunitiesModule { }
