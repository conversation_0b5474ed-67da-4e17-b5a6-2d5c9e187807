import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { CommunitiesService } from './communities.service';
import { CommunityRepository, UserRepository } from '@/database/repositories';
import {
  Community,
  CommunityMember,
  CommunityInvite,
  User,
  MemberRole,
  CommunityVisibility,
  InvitePermission,
  PostModeration,
  UserStatus
} from '@/types/global';

describe('CommunitiesService', () => {
  let service: CommunitiesService;
  let communityRepository: jest.Mocked<CommunityRepository>;
  let userRepository: jest.Mocked<UserRepository>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'test.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 5,
      commentsToday: 10,
      votesToday: 15,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCommunity: Community = {
    id: 'community-1',
    name: 'Test Community',
    description: 'A test community for fishing enthusiasts',
    rules: 'Be respectful and follow community guidelines',
    visibility: CommunityVisibility.Public,
    invitePermission: InvitePermission.Moderators,
    postModeration: PostModeration.None,
    commentModeration: PostModeration.None,
    pic: 'community.jpg',
    banner: 'community-banner.jpg',
    owner: mockUser,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockMember: CommunityMember = {
    id: 'member-1',
    user: mockUser,
    community: mockCommunity,
    role: MemberRole.Member,
    joinedAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockCommunityRepository = {
      createCommunity: jest.fn(),
      findCommunityById: jest.fn(),
      findCommunityByName: jest.fn(),
      updateCommunity: jest.fn(),
      deleteCommunity: jest.fn(),
      findPublicCommunities: jest.fn(),
      addCommunityMember: jest.fn(),
      removeCommunityMember: jest.fn(),
      updateMemberRole: jest.fn(),
      getCommunityMembers: jest.fn(),
      getUserMembership: jest.fn(),
      isUserMember: jest.fn(),
      getUserCommunities: jest.fn(),
      createCommunityInvite: jest.fn(),
      getCommunityInvites: jest.fn(),
      getUserInvites: jest.fn(),
      updateInviteStatus: jest.fn(),
      deleteInvite: jest.fn(),
      findInviteById: jest.fn(),
      hasExistingInvite: jest.fn(),
    };

    const mockUserRepository = {
      findUserById: jest.fn(),
      findUserByIdOrUsername: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CommunitiesService,
        {
          provide: CommunityRepository,
          useValue: mockCommunityRepository,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<CommunitiesService>(CommunitiesService);
    communityRepository = module.get(CommunityRepository);
    userRepository = module.get(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createCommunity', () => {
    it('should create a community successfully', async () => {
      const createDto = {
        name: 'New Community',
        description: 'A new fishing community',
        rules: 'Follow the rules and be respectful',
      };

      communityRepository.findCommunityByName.mockResolvedValue(null);
      communityRepository.createCommunity.mockResolvedValue(mockCommunity);
      communityRepository.addCommunityMember.mockResolvedValue(mockMember);
      communityRepository.getCommunityMembers.mockResolvedValue({ members: [mockMember], total: 1 });

      const result = await service.createCommunity(createDto, mockUser.id);

      expect(result).toBeDefined();
      expect(result.name).toBe(mockCommunity.name);
      expect(communityRepository.findCommunityByName).toHaveBeenCalledWith(createDto.name);
      expect(communityRepository.createCommunity).toHaveBeenCalledWith({
        ...createDto,
        ownerId: mockUser.id,
      });
      expect(communityRepository.addCommunityMember).toHaveBeenCalledWith(
        mockCommunity.id,
        mockUser.id,
        MemberRole.Admin
      );
    });

    it('should throw ConflictException if community name already exists', async () => {
      const createDto = {
        name: 'Existing Community',
        description: 'A community that already exists',
        rules: 'Follow the rules',
      };

      communityRepository.findCommunityByName.mockResolvedValue(mockCommunity);

      await expect(service.createCommunity(createDto, mockUser.id)).rejects.toThrow(
        ConflictException
      );
    });
  });

  describe('getCommunity', () => {
    it('should return a community for public communities', async () => {
      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);
      communityRepository.getCommunityMembers.mockResolvedValue({ members: [], total: 0 });

      const result = await service.getCommunity(mockCommunity.id);

      expect(result).toBeDefined();
      expect(result.id).toBe(mockCommunity.id);
      expect(communityRepository.findCommunityById).toHaveBeenCalledWith(mockCommunity.id);
    });

    it('should throw NotFoundException if community does not exist', async () => {
      communityRepository.findCommunityById.mockResolvedValue(null);

      await expect(service.getCommunity('non-existent-id')).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException for private community if user is not a member', async () => {
      const privateCommunity = { ...mockCommunity, visibility: CommunityVisibility.Private };
      communityRepository.findCommunityById.mockResolvedValue(privateCommunity);
      communityRepository.isUserMember.mockResolvedValue(false);

      await expect(service.getCommunity(privateCommunity.id, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('joinCommunity', () => {
    it('should allow user to join a public community', async () => {
      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);
      communityRepository.getUserMembership.mockResolvedValue(null);
      communityRepository.addCommunityMember.mockResolvedValue(mockMember);

      const result = await service.joinCommunity(mockCommunity.id, mockUser.id);

      expect(result).toBeDefined();
      expect(result.role).toBe(MemberRole.Member);
      expect(communityRepository.addCommunityMember).toHaveBeenCalledWith(
        mockCommunity.id,
        mockUser.id
      );
    });

    it('should throw ConflictException if user is already a member', async () => {
      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);
      communityRepository.getUserMembership.mockResolvedValue(mockMember);

      await expect(service.joinCommunity(mockCommunity.id, mockUser.id)).rejects.toThrow(
        ConflictException
      );
    });

    it('should throw ForbiddenException for private communities', async () => {
      const privateCommunity = { ...mockCommunity, visibility: CommunityVisibility.Private };
      communityRepository.findCommunityById.mockResolvedValue(privateCommunity);
      communityRepository.getUserMembership.mockResolvedValue(null);

      await expect(service.joinCommunity(privateCommunity.id, mockUser.id)).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('leaveCommunity', () => {
    it('should allow user to leave a community', async () => {
      const otherUser = { ...mockUser, id: 'other-user' };
      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);
      communityRepository.removeCommunityMember.mockResolvedValue(true);

      await service.leaveCommunity(mockCommunity.id, otherUser.id);

      expect(communityRepository.removeCommunityMember).toHaveBeenCalledWith(
        mockCommunity.id,
        otherUser.id
      );
    });

    it('should throw ForbiddenException if owner tries to leave', async () => {
      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);

      await expect(service.leaveCommunity(mockCommunity.id, mockUser.id)).rejects.toThrow(
        ForbiddenException
      );
    });

    it('should throw NotFoundException if user is not a member', async () => {
      const otherUser = { ...mockUser, id: 'other-user' };
      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);
      communityRepository.removeCommunityMember.mockResolvedValue(false);

      await expect(service.leaveCommunity(mockCommunity.id, otherUser.id)).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe('updateMemberRole', () => {
    it('should allow community owner to update member roles', async () => {
      const targetMember = { ...mockMember, user: { ...mockUser, id: 'target-user' } };
      const updateDto = { role: MemberRole.Moderator };

      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);
      communityRepository.getUserMembership.mockResolvedValue(targetMember);
      communityRepository.updateMemberRole.mockResolvedValue({
        ...targetMember,
        role: MemberRole.Moderator,
      });

      const result = await service.updateMemberRole(
        mockCommunity.id,
        'target-user',
        updateDto,
        mockUser.id
      );

      expect(result.role).toBe(MemberRole.Moderator);
      expect(communityRepository.updateMemberRole).toHaveBeenCalledWith(
        mockCommunity.id,
        'target-user',
        MemberRole.Moderator
      );
    });

    it('should throw ForbiddenException when trying to change owner role', async () => {
      const updateDto = { role: MemberRole.Member };

      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);
      communityRepository.getUserMembership.mockResolvedValue(mockMember);

      await expect(
        service.updateMemberRole(mockCommunity.id, mockUser.id, updateDto, mockUser.id)
      ).rejects.toThrow(ForbiddenException);
    });
  });
});
