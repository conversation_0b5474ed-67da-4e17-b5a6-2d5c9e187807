import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  ConflictException
} from '@nestjs/common';
import { CommunityRepository } from '@/database/repositories';
import { UserRepository } from '@/database/repositories';
import {
  Community,
  CommunityMember,
  CommunityInvite,
  User,
  MemberRole,
  CommunityVisibility,
  InvitePermission
} from '@/types/global';
import {
  CreateCommunityDto,
  UpdateCommunityDto,
  CommunitySearchQueryDto,
  UpdateMemberRoleDto,
  CreateCommunityInviteDto,
  UpdateInviteStatusDto,
  CommunityResponseDto,
  CommunityListResponseDto,
  CommunityMemberResponseDto,
  CommunityMembersResponseDto,
  CommunityInviteResponseDto,
  CommunityInvitesResponseDto,
  PublicUserProfileDto,
  PaginationQueryDto,
  InviteStatusQueryDto
} from './dto/communities.dto';

@Injectable()
export class CommunitiesService {
  private readonly logger = new Logger(CommunitiesService.name);

  constructor(
    private readonly communityRepository: CommunityRepository,
    private readonly userRepository: UserRepository,
  ) { }

  // Helper method to convert User to PublicUserProfileDto
  private userToPublicProfile (user: User): PublicUserProfileDto {
    return {
      id: user.id,
      uname: user.uname,
      profile: user.profile,
      activity: user.activity,
      createdAt: user.createdAt,
    };
  }

  // Helper method to convert Community to CommunityResponseDto
  private async communityToResponse (
    community: Community,
    currentUserId?: string
  ): Promise<CommunityResponseDto> {
    let memberCount = 0;
    let isUserMember = false;
    let userRole: MemberRole | undefined;

    try {
      // Get member count
      const { total } = await this.communityRepository.getCommunityMembers(community.id, 1, 1);
      memberCount = total;

      // Check if current user is a member
      if (currentUserId) {
        const membership = await this.communityRepository.getUserMembership(community.id, currentUserId);
        if (membership) {
          isUserMember = true;
          userRole = membership.role;
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to get community stats for ${community.id}: ${error.message}`);
    }

    return {
      id: community.id,
      name: community.name,
      description: community.description,
      rules: community.rules,
      visibility: community.visibility,
      invitePermission: community.invitePermission,
      postModeration: community.postModeration,
      commentModeration: community.commentModeration,
      pic: community.pic,
      banner: community.banner,
      owner: this.userToPublicProfile(community.owner),
      memberCount,
      isUserMember,
      userRole,
      createdAt: community.createdAt,
      updatedAt: community.updatedAt,
    };
  }

  // Helper method to check if user can perform action
  private async checkPermission (
    communityId: string,
    userId: string,
    requiredRole: MemberRole
  ): Promise<CommunityMember> {
    const membership = await this.communityRepository.getUserMembership(communityId, userId);

    if (!membership) {
      throw new ForbiddenException('You are not a member of this community');
    }

    const roleHierarchy = {
      [MemberRole.Member]: 0,
      [MemberRole.Moderator]: 1,
      [MemberRole.Admin]: 2,
    };

    if (roleHierarchy[membership.role] < roleHierarchy[requiredRole]) {
      throw new ForbiddenException('Insufficient permissions');
    }

    return membership;
  }

  // Community CRUD operations
  async createCommunity (createDto: CreateCommunityDto, ownerId: string): Promise<CommunityResponseDto> {
    try {
      // Check if community name already exists
      const existingCommunity = await this.communityRepository.findCommunityByName(createDto.name);
      if (existingCommunity) {
        throw new ConflictException('Community name already exists');
      }

      // Create the community
      const community = await this.communityRepository.createCommunity({
        ...createDto,
        ownerId,
      });

      // Add the owner as an admin member
      await this.communityRepository.addCommunityMember(community.id, ownerId, MemberRole.Admin);

      this.logger.log(`Created community: ${community.name} (${community.id}) by user ${ownerId}`);
      return this.communityToResponse(community, ownerId);
    } catch (error) {
      this.logger.error(`Failed to create community: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCommunity (id: string, currentUserId?: string): Promise<CommunityResponseDto> {
    const community = await this.communityRepository.findCommunityById(id);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Check if user can view private communities
    if (community.visibility === CommunityVisibility.Private && currentUserId) {
      const isMember = await this.communityRepository.isUserMember(id, currentUserId);
      if (!isMember && community.owner.id !== currentUserId) {
        throw new ForbiddenException('This community is private');
      }
    }

    return this.communityToResponse(community, currentUserId);
  }

  async updateCommunity (
    id: string,
    updateDto: UpdateCommunityDto,
    userId: string
  ): Promise<CommunityResponseDto> {
    const community = await this.communityRepository.findCommunityById(id);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Only owner or admins can update community
    if (community.owner.id !== userId) {
      await this.checkPermission(id, userId, MemberRole.Admin);
    }

    // Check if new name conflicts (if name is being changed)
    if (updateDto.name && updateDto.name !== community.name) {
      const existingCommunity = await this.communityRepository.findCommunityByName(updateDto.name);
      if (existingCommunity) {
        throw new ConflictException('Community name already exists');
      }
    }

    const updatedCommunity = await this.communityRepository.updateCommunity(id, updateDto);

    if (!updatedCommunity) {
      throw new NotFoundException('Community not found after update');
    }

    this.logger.log(`Updated community: ${id} by user ${userId}`);
    return this.communityToResponse(updatedCommunity, userId);
  }

  async deleteCommunity (id: string, userId: string): Promise<void> {
    const community = await this.communityRepository.findCommunityById(id);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Only owner can delete community
    if (community.owner.id !== userId) {
      throw new ForbiddenException('Only the community owner can delete the community');
    }

    const deleted = await this.communityRepository.deleteCommunity(id);

    if (!deleted) {
      throw new NotFoundException('Community not found');
    }

    this.logger.log(`Deleted community: ${id} by user ${userId}`);
  }

  async getPublicCommunities (query: CommunitySearchQueryDto): Promise<CommunityListResponseDto> {
    const { communities, total } = await this.communityRepository.findPublicCommunities(
      query.page || 1,
      query.limit || 20,
      query.search
    );

    const communityResponses = await Promise.all(
      communities.map(community => this.communityToResponse(community))
    );

    const totalPages = Math.ceil(total / (query.limit || 20));

    return {
      communities: communityResponses,
      total,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  // Community membership operations
  async joinCommunity (communityId: string, userId: string): Promise<CommunityMemberResponseDto> {
    const community = await this.communityRepository.findCommunityById(communityId);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Check if user is already a member
    const existingMembership = await this.communityRepository.getUserMembership(communityId, userId);
    if (existingMembership) {
      throw new ConflictException('You are already a member of this community');
    }

    // Check if community is private (requires invitation)
    if (community.visibility === CommunityVisibility.Private) {
      throw new ForbiddenException('This community is private and requires an invitation');
    }

    const member = await this.communityRepository.addCommunityMember(communityId, userId);

    this.logger.log(`User ${userId} joined community ${communityId}`);

    return {
      id: member.id,
      user: this.userToPublicProfile(member.user),
      role: member.role,
      joinedAt: member.joinedAt,
    };
  }

  async leaveCommunity (communityId: string, userId: string): Promise<void> {
    const community = await this.communityRepository.findCommunityById(communityId);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Owner cannot leave their own community
    if (community.owner.id === userId) {
      throw new ForbiddenException('Community owner cannot leave the community');
    }

    const removed = await this.communityRepository.removeCommunityMember(communityId, userId);

    if (!removed) {
      throw new NotFoundException('You are not a member of this community');
    }

    this.logger.log(`User ${userId} left community ${communityId}`);
  }

  async getCommunityMembers (
    communityId: string,
    query: PaginationQueryDto,
    currentUserId?: string
  ): Promise<CommunityMembersResponseDto> {
    const community = await this.communityRepository.findCommunityById(communityId);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Check if user can view members (for private communities)
    if (community.visibility === CommunityVisibility.Private && currentUserId) {
      const isMember = await this.communityRepository.isUserMember(communityId, currentUserId);
      if (!isMember && community.owner.id !== currentUserId) {
        throw new ForbiddenException('You cannot view members of this private community');
      }
    }

    const { members, total } = await this.communityRepository.getCommunityMembers(
      communityId,
      query.page || 1,
      query.limit || 20
    );

    const memberResponses = members.map(member => ({
      id: member.id,
      user: this.userToPublicProfile(member.user),
      role: member.role,
      joinedAt: member.joinedAt,
    }));

    const totalPages = Math.ceil(total / (query.limit || 20));

    return {
      members: memberResponses,
      total,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  async updateMemberRole (
    communityId: string,
    memberId: string,
    updateDto: UpdateMemberRoleDto,
    currentUserId: string
  ): Promise<CommunityMemberResponseDto> {
    const community = await this.communityRepository.findCommunityById(communityId);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Get the target member
    const targetMember = await this.communityRepository.getUserMembership(communityId, memberId);
    if (!targetMember) {
      throw new NotFoundException('Member not found in this community');
    }

    // Only owner or admins can update roles
    if (community.owner.id !== currentUserId) {
      await this.checkPermission(communityId, currentUserId, MemberRole.Admin);
    }

    // Cannot change owner's role
    if (community.owner.id === memberId) {
      throw new ForbiddenException('Cannot change the community owner\'s role');
    }

    // Cannot promote to admin unless you're the owner
    if (updateDto.role === MemberRole.Admin && community.owner.id !== currentUserId) {
      throw new ForbiddenException('Only the community owner can promote members to admin');
    }

    const updatedMember = await this.communityRepository.updateMemberRole(
      communityId,
      memberId,
      updateDto.role
    );

    if (!updatedMember) {
      throw new NotFoundException('Member not found after update');
    }

    this.logger.log(`Updated member ${memberId} role to ${updateDto.role} in community ${communityId}`);

    return {
      id: updatedMember.id,
      user: this.userToPublicProfile(updatedMember.user),
      role: updatedMember.role,
      joinedAt: updatedMember.joinedAt,
    };
  }

  async getUserCommunities (
    userId: string,
    query: PaginationQueryDto
  ): Promise<CommunityListResponseDto> {
    const { communities, total } = await this.communityRepository.getUserCommunities(
      userId,
      query.page || 1,
      query.limit || 20
    );

    const communityResponses = await Promise.all(
      communities.map(community => this.communityToResponse(community, userId))
    );

    const totalPages = Math.ceil(total / (query.limit || 20));

    return {
      communities: communityResponses,
      total,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  // Community invitation operations
  async createInvite (
    communityId: string,
    createDto: CreateCommunityInviteDto,
    invitedById: string
  ): Promise<CommunityInviteResponseDto> {
    const community = await this.communityRepository.findCommunityById(communityId);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Check if inviter has permission to invite
    const inviterMembership = await this.communityRepository.getUserMembership(communityId, invitedById);
    if (!inviterMembership && community.owner.id !== invitedById) {
      throw new ForbiddenException('You are not a member of this community');
    }

    // Check invite permissions
    if (community.owner.id !== invitedById) {
      const requiredRole = {
        [InvitePermission.Anyone]: MemberRole.Member,
        [InvitePermission.Members]: MemberRole.Member,
        [InvitePermission.Moderators]: MemberRole.Moderator,
        [InvitePermission.Admins]: MemberRole.Admin,
      }[community.invitePermission];

      await this.checkPermission(communityId, invitedById, requiredRole);
    }

    // Check if invited user exists
    const invitedUser = await this.userRepository.findUserById(createDto.invitedUserId);
    if (!invitedUser) {
      throw new NotFoundException('Invited user not found');
    }

    // Check if user is already a member
    const existingMembership = await this.communityRepository.getUserMembership(
      communityId,
      createDto.invitedUserId
    );
    if (existingMembership) {
      throw new ConflictException('User is already a member of this community');
    }

    // Check if there's already a pending invite
    const hasExistingInvite = await this.communityRepository.hasExistingInvite(
      communityId,
      createDto.invitedUserId
    );
    if (hasExistingInvite) {
      throw new ConflictException('User already has a pending invite to this community');
    }

    const invite = await this.communityRepository.createCommunityInvite(
      communityId,
      invitedById,
      createDto.invitedUserId,
      createDto.role || MemberRole.Member
    );

    this.logger.log(`Created invite for user ${createDto.invitedUserId} to community ${communityId}`);

    return {
      id: invite.id,
      community: {
        id: invite.community.id,
        name: invite.community.name,
        description: invite.community.description,
        pic: invite.community.pic,
      },
      invitedBy: this.userToPublicProfile(invite.invitedBy),
      invitedUser: this.userToPublicProfile(invite.invitedUser),
      role: invite.role,
      status: invite.status,
      createdAt: invite.createdAt,
    };
  }

  async getCommunityInvites (
    communityId: string,
    query: InviteStatusQueryDto,
    currentUserId: string
  ): Promise<CommunityInvitesResponseDto> {
    const community = await this.communityRepository.findCommunityById(communityId);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Only moderators and above can view community invites
    if (community.owner.id !== currentUserId) {
      await this.checkPermission(communityId, currentUserId, MemberRole.Moderator);
    }

    const { invites, total } = await this.communityRepository.getCommunityInvites(
      communityId,
      query.status,
      query.page || 1,
      query.limit || 20
    );

    const inviteResponses = invites.map(invite => ({
      id: invite.id,
      community: {
        id: invite.community.id,
        name: invite.community.name,
        description: invite.community.description,
        pic: invite.community.pic,
      },
      invitedBy: this.userToPublicProfile(invite.invitedBy),
      invitedUser: this.userToPublicProfile(invite.invitedUser),
      role: invite.role,
      status: invite.status,
      createdAt: invite.createdAt,
    }));

    const totalPages = Math.ceil(total / (query.limit || 20));

    return {
      invites: inviteResponses,
      total,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  async getUserInvites (
    userId: string,
    query: InviteStatusQueryDto
  ): Promise<CommunityInvitesResponseDto> {
    const { invites, total } = await this.communityRepository.getUserInvites(
      userId,
      query.status,
      query.page || 1,
      query.limit || 20
    );

    const inviteResponses = invites.map(invite => ({
      id: invite.id,
      community: {
        id: invite.community.id,
        name: invite.community.name,
        description: invite.community.description,
        pic: invite.community.pic,
      },
      invitedBy: this.userToPublicProfile(invite.invitedBy),
      invitedUser: this.userToPublicProfile(invite.invitedUser),
      role: invite.role,
      status: invite.status,
      createdAt: invite.createdAt,
    }));

    const totalPages = Math.ceil(total / (query.limit || 20));

    return {
      invites: inviteResponses,
      total,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  async respondToInvite (
    inviteId: string,
    updateDto: UpdateInviteStatusDto,
    userId: string
  ): Promise<CommunityInviteResponseDto> {
    const invite = await this.communityRepository.findInviteById(inviteId);

    if (!invite) {
      throw new NotFoundException('Invite not found');
    }

    // Only the invited user can respond to the invite
    if (invite.invitedUser.id !== userId) {
      throw new ForbiddenException('You can only respond to your own invites');
    }

    // Can only respond to pending invites
    if (invite.status !== 'Pending') {
      throw new BadRequestException('This invite has already been responded to');
    }

    const updatedInvite = await this.communityRepository.updateInviteStatus(inviteId, updateDto.status);

    if (!updatedInvite) {
      throw new NotFoundException('Invite not found after update');
    }

    // If accepted, add user to community
    if (updateDto.status === 'Accepted') {
      await this.communityRepository.addCommunityMember(
        invite.community.id,
        userId,
        invite.role
      );
      this.logger.log(`User ${userId} accepted invite and joined community ${invite.community.id}`);
    }

    this.logger.log(`User ${userId} ${updateDto.status.toLowerCase()} invite ${inviteId}`);

    return {
      id: updatedInvite.id,
      community: {
        id: updatedInvite.community.id,
        name: updatedInvite.community.name,
        description: updatedInvite.community.description,
        pic: updatedInvite.community.pic,
      },
      invitedBy: this.userToPublicProfile(updatedInvite.invitedBy),
      invitedUser: this.userToPublicProfile(updatedInvite.invitedUser),
      role: updatedInvite.role,
      status: updatedInvite.status,
      createdAt: updatedInvite.createdAt,
    };
  }

  async deleteInvite (inviteId: string, currentUserId: string): Promise<void> {
    const invite = await this.communityRepository.findInviteById(inviteId);

    if (!invite) {
      throw new NotFoundException('Invite not found');
    }

    // Only the inviter, invited user, or community moderators can delete invites
    const canDelete =
      invite.invitedBy.id === currentUserId ||
      invite.invitedUser.id === currentUserId ||
      invite.community.owner.id === currentUserId;

    if (!canDelete) {
      try {
        await this.checkPermission(invite.community.id, currentUserId, MemberRole.Moderator);
      } catch {
        throw new ForbiddenException('You cannot delete this invite');
      }
    }

    const deleted = await this.communityRepository.deleteInvite(inviteId);

    if (!deleted) {
      throw new NotFoundException('Invite not found');
    }

    this.logger.log(`Deleted invite ${inviteId} by user ${currentUserId}`);
  }
}
