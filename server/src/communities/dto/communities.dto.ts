import {
  IsString,
  IsEnum,
  IsOptional,
  IsUUID,
  IsInt,
  Min,
  Max,
  Length,
  IsNotEmpty,
  ValidateNested,
  IsArray
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import {
  CommunityVisibility,
  InvitePermission,
  PostModeration,
  MemberRole,
  Community,
  CommunityMember,
  CommunityInvite,
  User
} from '@/types/global';

// Community Creation DTO
export class CreateCommunityDto {
  @IsString()
  @IsNotEmpty()
  @Length(3, 100)
  name: string;

  @IsString()
  @IsNotEmpty()
  @Length(10, 2000)
  description: string;

  @IsString()
  @IsNotEmpty()
  @Length(10, 5000)
  rules: string;

  @IsEnum(CommunityVisibility)
  @IsOptional()
  visibility?: CommunityVisibility = CommunityVisibility.Public;

  @IsEnum(InvitePermission)
  @IsOptional()
  invitePermission?: InvitePermission = InvitePermission.Moderators;

  @IsEnum(PostModeration)
  @IsOptional()
  postModeration?: PostModeration = PostModeration.None;

  @IsEnum(PostModeration)
  @IsOptional()
  commentModeration?: PostModeration = PostModeration.None;

  @IsString()
  @IsOptional()
  pic?: string;

  @IsString()
  @IsOptional()
  banner?: string;
}

// Community Update DTO
export class UpdateCommunityDto {
  @IsString()
  @IsOptional()
  @Length(3, 100)
  name?: string;

  @IsString()
  @IsOptional()
  @Length(10, 2000)
  description?: string;

  @IsString()
  @IsOptional()
  @Length(10, 5000)
  rules?: string;

  @IsEnum(CommunityVisibility)
  @IsOptional()
  visibility?: CommunityVisibility;

  @IsEnum(InvitePermission)
  @IsOptional()
  invitePermission?: InvitePermission;

  @IsEnum(PostModeration)
  @IsOptional()
  postModeration?: PostModeration;

  @IsEnum(PostModeration)
  @IsOptional()
  commentModeration?: PostModeration;

  @IsString()
  @IsOptional()
  pic?: string;

  @IsString()
  @IsOptional()
  banner?: string;
}

// Community Search Query DTO
export class CommunitySearchQueryDto {
  @IsOptional()
  @IsString()
  @Length(1, 100)
  search?: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsEnum(CommunityVisibility)
  visibility?: CommunityVisibility;
}

// Member Role Update DTO
export class UpdateMemberRoleDto {
  @IsEnum(MemberRole)
  role: MemberRole;
}

// Community Invite Creation DTO
export class CreateCommunityInviteDto {
  @IsUUID()
  invitedUserId: string;

  @IsEnum(MemberRole)
  @IsOptional()
  role?: MemberRole = MemberRole.Member;
}

// Invite Status Update DTO
export class UpdateInviteStatusDto {
  @IsEnum(['Accepted', 'Rejected'])
  status: 'Accepted' | 'Rejected';
}

// Response DTOs
export class PublicUserProfileDto {
  id: string;
  uname: string;
  profile: {
    displayName: string;
    bio?: string;
    location?: string;
    website?: string;
    pic?: string;
    banner?: string;
  };
  activity: {
    postsToday: number;
    commentsToday: number;
    votesToday: number;
    lastActivityAt: Date;
  };
  createdAt: Date;
}

export class CommunityResponseDto {
  id: string;
  name: string;
  description: string;
  rules: string;
  visibility: CommunityVisibility;
  invitePermission: InvitePermission;
  postModeration: PostModeration;
  commentModeration: PostModeration;
  pic?: string;
  banner?: string;
  owner: PublicUserProfileDto;
  memberCount?: number;
  isUserMember?: boolean;
  userRole?: MemberRole;
  createdAt: Date;
  updatedAt: Date;
}

export class CommunityListResponseDto {
  communities: CommunityResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class CommunityMemberResponseDto {
  id: string;
  user: PublicUserProfileDto;
  role: MemberRole;
  joinedAt: Date;
}

export class CommunityMembersResponseDto {
  members: CommunityMemberResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class CommunityInviteResponseDto {
  id: string;
  community: {
    id: string;
    name: string;
    description: string;
    pic?: string;
  };
  invitedBy: PublicUserProfileDto;
  invitedUser: PublicUserProfileDto;
  role: MemberRole;
  status: 'Pending' | 'Accepted' | 'Rejected';
  createdAt: Date;
}

export class CommunityInvitesResponseDto {
  invites: CommunityInviteResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Pagination Query DTO
export class PaginationQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

// Invite Status Query DTO
export class InviteStatusQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsEnum(['Pending', 'Accepted', 'Rejected'])
  status?: 'Pending' | 'Accepted' | 'Rejected';
}
