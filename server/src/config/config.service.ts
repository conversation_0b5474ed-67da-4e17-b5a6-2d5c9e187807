import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';

@Injectable()
export class AppConfigService {
  constructor(private configService: NestConfigService) { }

  // Database Configuration
  get databaseHost (): string {
    return this.configService.get<string>('DATABASE_HOST', 'localhost');
  }

  get databasePort (): number {
    return this.configService.get<number>('DATABASE_PORT', 5432);
  }

  get databaseUser (): string {
    return this.configService.get<string>('DATABASE_USER', 'lmbuser');
  }

  get databasePassword (): string {
    return this.configService.get<string>('DATABASE_PASSWORD', '');
  }

  get databaseName (): string {
    return this.configService.get<string>('DATABASE_NAME', 'lmb');
  }

  // JWT Configuration
  get jwtSecret (): string {
    return this.configService.get<string>('JWT_SECRET', 'default-secret-change-in-production');
  }

  get jwtExpiresIn (): string {
    return this.configService.get<string>('JWT_EXPIRES_IN', '7d');
  }

  // GitHub OAuth Configuration
  get githubClientId (): string {
    return this.configService.get<string>('GITHUB_CLIENT_ID', '');
  }

  get githubClientSecret (): string {
    return this.configService.get<string>('GITHUB_CLIENT_SECRET', '');
  }

  get githubCallbackUrl (): string {
    return this.configService.get<string>('GITHUB_CALLBACK_URL', 'http://localhost:3000/v1/auth/github/callback');
  }

  // Application Configuration
  get nodeEnv (): string {
    return this.configService.get<string>('NODE_ENV', 'development');
  }

  get port (): number {
    return this.configService.get<number>('PORT', 3000);
  }

  get appUrl (): string {
    return this.configService.get<string>('APP_URL', 'http://localhost:3000');
  }

  get isDevelopment (): boolean {
    return this.nodeEnv === 'development';
  }

  get isProduction (): boolean {
    return this.nodeEnv === 'production';
  }

  // Rate Limiting Configuration
  get throttleTtl (): number {
    return this.configService.get<number>('THROTTLE_TTL', 60);
  }

  get throttleLimit (): number {
    return this.configService.get<number>('THROTTLE_LIMIT', 10);
  }

  // Web Frontend URL (for OAuth redirects)
  get webUrl (): string {
    return this.configService.get<string>('WEB_URL', 'http://localhost:3001');
  }

  // Super Users Configuration
  get superUsers (): string[] {
    const superUsersEnv = this.configService.get<string>('SUPER_USERS', '');
    return superUsersEnv ? superUsersEnv.split(',').map(username => username.trim()) : [];
  }

  // Helper method to check if a username is a super user
  isSuperUser (username: string): boolean {
    return this.superUsers.includes(username);
  }
}
