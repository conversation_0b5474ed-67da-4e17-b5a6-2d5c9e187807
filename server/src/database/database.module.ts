import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppConfigService } from '../config/config.service';
import { DatabaseService } from './database.service';
import * as entities from './entities';

@Global()
@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [AppConfigService],
      useFactory: (configService: AppConfigService) => ({
        type: 'postgres',
        host: configService.databaseHost,
        port: configService.databasePort,
        username: configService.databaseUser,
        password: configService.databasePassword,
        database: configService.databaseName,
        entities: Object.values(entities),
        synchronize: false, // Always use migrations
        logging: configService.isDevelopment,
        ssl: configService.isProduction ? { rejectUnauthorized: false } : false,
      }),
    }),
    TypeOrmModule.forFeature(Object.values(entities)),
  ],
  providers: [DatabaseService],
  exports: [DatabaseService, TypeOrmModule],
})
export class DatabaseModule { }
