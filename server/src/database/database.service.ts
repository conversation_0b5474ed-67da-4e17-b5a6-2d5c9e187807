import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';

@Injectable()
export class DatabaseService {
  private readonly logger = new Logger(DatabaseService.name);

  constructor(private dataSource: DataSource) { }

  // Health check method
  async healthCheck (): Promise<boolean> {
    try {
      // Check if the data source is initialized and connected
      if (!this.dataSource.isInitialized) {
        this.logger.error('Database connection not initialized');
        return false;
      }

      // Run a simple query to test the connection
      await this.dataSource.query('SELECT 1');
      return true;
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return false;
    }
  }

  // Get the data source for direct access if needed
  getDataSource (): DataSource {
    return this.dataSource;
  }
}
