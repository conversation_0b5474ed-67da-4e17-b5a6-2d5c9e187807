import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { VoteType } from '@/types/global';
import { UserEntity } from './user.entity';
import { CommentEntity } from './comment.entity';

@Entity('comment_votes')
@Unique(['userId', 'commentId'])
@Index(['userId'])
@Index(['commentId'])
@Index(['voteType'])
@Index(['createdAt'])
export class CommentVoteEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'comment_id' })
  commentId: string;

  @Column({
    type: 'enum',
    enum: VoteType,
    name: 'vote_type',
  })
  @Index()
  voteType: VoteType;

  @CreateDateColumn({ name: 'created_at' })
  @Index()
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => UserEntity, (user) => user.commentVotes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => CommentEntity, (comment) => comment.votes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'comment_id' })
  comment: CommentEntity;
}
