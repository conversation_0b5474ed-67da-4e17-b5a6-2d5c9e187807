import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
  Tree,
  TreeParent,
  TreeChildren,
} from 'typeorm';
import { CommentStatus } from '@/types/global';
import { UserEntity } from './user.entity';
import { PostEntity } from './post.entity';
import { CommentVoteEntity } from './comment-vote.entity';

@Entity('comments')
@Tree('materialized-path')
@Index(['authorId'])
@Index(['postId'])
@Index(['status'])
@Index(['createdAt'])
@Index(['parentCommentId'])
export class CommentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'text' })
  content: string;

  @Column({
    type: 'enum',
    enum: CommentStatus,
    default: CommentStatus.Published,
  })
  @Index()
  status: CommentStatus;

  @Column({ name: 'author_id' })
  authorId: string;

  @Column({ name: 'post_id' })
  postId: string;

  @Column({ name: 'parent_comment_id', nullable: true })
  @Index()
  parentCommentId?: string;

  // Moderation data as JSONB
  @Column({ type: 'jsonb', nullable: true })
  moderation?: {
    moderatedBy: string; // User ID
    moderatedAt: Date;
    action: string;
    logs: Array<{
      id: string;
      message: string;
      createdBy: string; // User ID
      createdAt: Date;
    }>;
  };

  @Column({ default: 0 })
  upvotes: number;

  @Column({ default: 0 })
  downvotes: number;

  @CreateDateColumn({ name: 'created_at' })
  @Index()
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => UserEntity, (user) => user.comments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'author_id' })
  author: UserEntity;

  @ManyToOne(() => PostEntity, (post) => post.comments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'post_id' })
  post: PostEntity;

  // Tree structure for nested comments
  @TreeParent()
  parentComment?: CommentEntity;

  @TreeChildren()
  childComments: CommentEntity[];

  @OneToMany(() => CommentVoteEntity, (vote) => vote.comment)
  votes: CommentVoteEntity[];
}
