import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { MemberRole } from '@/types/global';
import { UserEntity } from './user.entity';
import { CommunityEntity } from './community.entity';

@Entity('community_invites')
@Index(['communityId'])
@Index(['invitedById'])
@Index(['invitedUserId'])
@Index(['status'])
@Index(['createdAt'])
export class CommunityInviteEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'community_id' })
  communityId: string;

  @Column({ name: 'invited_by_id' })
  invitedById: string;

  @Column({ name: 'invited_user_id' })
  invitedUserId: string;

  @Column({
    type: 'enum',
    enum: MemberRole,
    default: MemberRole.Member,
  })
  role: MemberRole;

  @Column({
    type: 'enum',
    enum: ['Pending', 'Accepted', 'Rejected'],
    default: 'Pending',
  })
  @Index()
  status: 'Pending' | 'Accepted' | 'Rejected';

  @CreateDateColumn({ name: 'created_at' })
  @Index()
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => CommunityEntity, (community) => community.invites, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'community_id' })
  community: CommunityEntity;

  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'invited_by_id' })
  invitedBy: UserEntity;

  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'invited_user_id' })
  invitedUser: UserEntity;
}
