import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { MemberRole } from '@/types/global';
import { UserEntity } from './user.entity';
import { CommunityEntity } from './community.entity';

@Entity('community_members')
@Unique(['userId', 'communityId'])
@Index(['userId'])
@Index(['communityId'])
@Index(['role'])
@Index(['joinedAt'])
export class CommunityMemberEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'community_id' })
  communityId: string;

  @Column({
    type: 'enum',
    enum: MemberRole,
    default: MemberRole.Member,
  })
  @Index()
  role: MemberRole;

  @CreateDateColumn({ name: 'joined_at' })
  @Index()
  joinedAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => CommunityEntity, (community) => community.members, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'community_id' })
  community: CommunityEntity;
}
