import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinColumn,
  JoinTable,
  Index,
} from 'typeorm';
import { CommunityVisibility, InvitePermission, PostModeration } from '@/types/global';
import { UserEntity } from './user.entity';
import { PostEntity } from './post.entity';
import { CommunityMemberEntity } from './community-member.entity';
import { CommunityInviteEntity } from './community-invite.entity';

@Entity('communities')
@Index(['name'])
@Index(['visibility'])
@Index(['ownerId'])
export class CommunityEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 100 })
  @Index()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'text' })
  rules: string;

  @Column({
    type: 'enum',
    enum: CommunityVisibility,
    default: CommunityVisibility.Public,
  })
  @Index()
  visibility: CommunityVisibility;

  @Column({
    type: 'enum',
    enum: InvitePermission,
    name: 'invite_permission',
    default: InvitePermission.Moderators,
  })
  invitePermission: InvitePermission;

  @Column({
    type: 'enum',
    enum: PostModeration,
    name: 'post_moderation',
    default: PostModeration.None,
  })
  postModeration: PostModeration;

  @Column({
    type: 'enum',
    enum: PostModeration,
    name: 'comment_moderation',
    default: PostModeration.None,
  })
  commentModeration: PostModeration;

  @Column({ nullable: true })
  pic: string;

  @Column({ nullable: true })
  banner: string;

  @Column({ name: 'owner_id' })
  ownerId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => UserEntity, (user) => user.ownedCommunities, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'owner_id' })
  owner: UserEntity;

  @OneToMany(() => PostEntity, (post) => post.community)
  posts: PostEntity[];

  @OneToMany(() => CommunityMemberEntity, (member) => member.community)
  members: CommunityMemberEntity[];

  @OneToMany(() => CommunityInviteEntity, (invite) => invite.community)
  invites: CommunityInviteEntity[];

  // Many-to-many relationship with users (subscribers)
  @ManyToMany(() => UserEntity, (user) => user.subscribedCommunities)
  @JoinTable({
    name: 'community_subscribers',
    joinColumn: { name: 'community_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
  })
  subscribers: UserEntity[];
}
