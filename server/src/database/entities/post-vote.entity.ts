import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { VoteType } from '@/types/global';
import { UserEntity } from './user.entity';
import { PostEntity } from './post.entity';

@Entity('post_votes')
@Unique(['userId', 'postId'])
@Index(['userId'])
@Index(['postId'])
@Index(['voteType'])
@Index(['createdAt'])
export class PostVoteEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'post_id' })
  postId: string;

  @Column({
    type: 'enum',
    enum: VoteType,
    name: 'vote_type',
  })
  @Index()
  voteType: VoteType;

  @CreateDateColumn({ name: 'created_at' })
  @Index()
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => UserEntity, (user) => user.postVotes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => PostEntity, (post) => post.votes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'post_id' })
  post: PostEntity;
}
