import {
  <PERSON><PERSON>ty,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
// Define TagStatus enum locally to avoid import issues during testing
enum TagStatus {
  Pending = 'Pending',
  Approved = 'Approved',
  Rejected = 'Rejected',
}
import { PostEntity } from './post.entity';
import { UserEntity } from './user.entity';
import { FishingSpotEntity } from './fishing-spot.entity';

@Entity('tags')
@Index(['status'])
@Index(['category'])
@Index(['createdBy'])
export class TagEntity {
  @PrimaryColumn({ length: 50 })
  name: string; // Normalized tag name (e.g., "largemouth-bass")

  @Column({ name: 'display_name', length: 50 })
  displayName: string; // Original display format (e.g., "Largemouth Bass")

  @Column({ length: 100, nullable: true })
  description?: string;

  @Column({ length: 50, nullable: true })
  @Index()
  category?: string;

  @Column({ default: '#3B82F6' })
  color: string;

  @Column({
    type: 'enum',
    enum: TagStatus,
    default: TagStatus.Pending,
  })
  @Index()
  status: TagStatus;

  @Column({ name: 'usage_count', default: 0 })
  usageCount: number;

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'moderated_by', nullable: true })
  moderatedBy?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'moderated_at', nullable: true })
  moderatedAt?: Date;

  // Relationships
  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: UserEntity;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'moderated_by' })
  moderator?: UserEntity;

  @ManyToMany(() => PostEntity, (post) => post.tags)
  posts: PostEntity[];

  @ManyToMany(() => FishingSpotEntity, (spot) => spot.tags)
  fishingSpots: FishingSpotEntity[];
}
