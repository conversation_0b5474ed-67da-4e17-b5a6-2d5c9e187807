import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
  Index,
} from 'typeorm';
import { UserStatus } from '@/types/global';
import { AuthTokenEntity } from './auth-token.entity';
import { SessionEntity } from './session.entity';
import { CommunityEntity } from './community.entity';
import { PostEntity } from './post.entity';
import { CommentEntity } from './comment.entity';
import { UserFollowEntity } from './user-follow.entity';
import { PostVoteEntity } from './post-vote.entity';
import { CommentVoteEntity } from './comment-vote.entity';

@Entity('users')
@Index(['email'])
@Index(['uname'])
@Index(['providerAccounts'], { where: 'provider_accounts IS NOT NULL' })
export class UserEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 50 })
  @Index()
  uname: string;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.Active,
  })
  status: UserStatus;

  @Column({ unique: true })
  @Index()
  email: string;

  @Column({ name: 'email_verified', default: false })
  emailVerified: boolean;

  // User profile as JSONB for flexibility
  @Column({ type: 'jsonb' })
  profile: {
    displayName: string;
    bio: string;
    location: string;
    website: string;
    pic: string;
    banner: string;
    isGuide: boolean;
    isVendor: boolean;
    socialLinks?: {
      twitter?: string;
      instagram?: string;
      youtube?: string;
      tiktok?: string;
    };
  };

  // User preferences as JSONB
  @Column({ type: 'jsonb' })
  prefs: {
    theme: 'Light' | 'Dark' | 'System';
    notificationPreferences: any[];
    emailDigest: 'Daily' | 'Weekly' | 'Never';
    contentVisibility: 'Public' | 'Private';
    twoFactorEnabled: boolean;
    sessionTimeout: 'Short' | 'Medium' | 'Long';
  };

  // User activity tracking as JSONB
  @Column({ type: 'jsonb' })
  activity: {
    postsToday: number;
    commentsToday: number;
    votesToday: number;
    lastActivityAt: Date;
  };

  // Saved content as JSONB array
  @Column({ type: 'jsonb', default: '[]', name: 'saved_content' })
  savedContent: Array<{
    id: string;
    postId?: string;
    communityId?: string;
    createdAt: Date;
  }>;

  // Provider accounts for OAuth (indexed for fast lookups)
  @Column({ type: 'jsonb', name: 'provider_accounts', nullable: true })
  @Index()
  providerAccounts: {
    google?: string;
    facebook?: string;
    github?: string;
    apple?: string;
  };

  @Column({ name: 'last_login_at', nullable: true })
  lastLoginAt?: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @OneToMany(() => AuthTokenEntity, (authToken) => authToken.user)
  authTokens: AuthTokenEntity[];

  @OneToMany(() => SessionEntity, (session) => session.user)
  sessions: SessionEntity[];

  @OneToMany(() => PostEntity, (post) => post.author)
  posts: PostEntity[];

  @OneToMany(() => CommentEntity, (comment) => comment.author)
  comments: CommentEntity[];

  // Communities the user owns
  @OneToMany(() => CommunityEntity, (community) => community.owner)
  ownedCommunities: CommunityEntity[];

  // Communities the user is subscribed to (many-to-many)
  @ManyToMany(() => CommunityEntity, (community) => community.subscribers)
  subscribedCommunities: CommunityEntity[];

  // Following relationships (who this user follows)
  @OneToMany(() => UserFollowEntity, (follow) => follow.follower)
  following: UserFollowEntity[];

  // Follower relationships (who follows this user)
  @OneToMany(() => UserFollowEntity, (follow) => follow.following)
  followers: UserFollowEntity[];

  // Post votes
  @OneToMany(() => PostVoteEntity, (vote) => vote.user)
  postVotes: PostVoteEntity[];

  // Comment votes
  @OneToMany(() => CommentVoteEntity, (vote) => vote.user)
  commentVotes: CommentVoteEntity[];
}
