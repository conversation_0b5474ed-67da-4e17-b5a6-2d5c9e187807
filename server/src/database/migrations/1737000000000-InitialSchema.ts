import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialSchema1737000000000 implements MigrationInterface {
  name = 'InitialSchema1737000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Since synchronize is enabled and tables already exist,
    // this migration serves as a baseline for future migrations
    console.log('Initial schema already created via synchronize mode');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop all tables if needed
    await queryRunner.query(`DROP TABLE IF EXISTS "community_members" CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS "post_tags" CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS "comments" CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS "posts" CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS "tags" CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS "fishing_spots" CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS "communities" CASCADE`);
    await queryRunner.query(`DROP TABLE IF EXISTS "users" CASCADE`);
    await queryRunner.query(`DROP TYPE IF EXISTS "tag_status_enum"`);
  }
}
