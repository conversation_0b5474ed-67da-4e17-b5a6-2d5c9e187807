import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPostFishingSpotsRelationship1752714395437 implements MigrationInterface {
    name = 'AddPostFishingSpotsRelationship1752714395437'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "auth_tokens" DROP CONSTRAINT "FK_auth_tokens_user"`);
        await queryRunner.query(`ALTER TABLE "sessions" DROP CONSTRAINT "FK_sessions_user"`);
        await queryRunner.query(`ALTER TABLE "comment_votes" DROP CONSTRAINT "FK_comment_votes_user"`);
        await queryRunner.query(`ALTER TABLE "comment_votes" DROP CONSTRAINT "FK_comment_votes_comment"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_comments_author"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_comments_post"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_comments_parent"`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" DROP CONSTRAINT "FK_fishing_spots_created_by"`);
        await queryRunner.query(`ALTER TABLE "post_votes" DROP CONSTRAINT "FK_post_votes_user"`);
        await queryRunner.query(`ALTER TABLE "post_votes" DROP CONSTRAINT "FK_post_votes_post"`);
        await queryRunner.query(`ALTER TABLE "posts" DROP CONSTRAINT "FK_posts_author"`);
        await queryRunner.query(`ALTER TABLE "posts" DROP CONSTRAINT "FK_posts_community"`);
        await queryRunner.query(`ALTER TABLE "posts" DROP CONSTRAINT "FK_posts_fishing_spot"`);
        await queryRunner.query(`ALTER TABLE "community_members" DROP CONSTRAINT "FK_community_members_user"`);
        await queryRunner.query(`ALTER TABLE "community_members" DROP CONSTRAINT "FK_community_members_community"`);
        await queryRunner.query(`ALTER TABLE "community_invites" DROP CONSTRAINT "FK_community_invites_community"`);
        await queryRunner.query(`ALTER TABLE "community_invites" DROP CONSTRAINT "FK_community_invites_invited_by"`);
        await queryRunner.query(`ALTER TABLE "community_invites" DROP CONSTRAINT "FK_community_invites_invited_user"`);
        await queryRunner.query(`ALTER TABLE "communities" DROP CONSTRAINT "FK_communities_owner"`);
        await queryRunner.query(`ALTER TABLE "user_follows" DROP CONSTRAINT "FK_user_follows_follower"`);
        await queryRunner.query(`ALTER TABLE "user_follows" DROP CONSTRAINT "FK_user_follows_following"`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP CONSTRAINT "FK_post_tags_post"`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP CONSTRAINT "FK_post_tags_tag"`);
        await queryRunner.query(`ALTER TABLE "community_subscribers" DROP CONSTRAINT "FK_community_subscribers_community"`);
        await queryRunner.query(`ALTER TABLE "community_subscribers" DROP CONSTRAINT "FK_community_subscribers_user"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_auth_tokens_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_auth_tokens_provider_user"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_sessions_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_sessions_token"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_sessions_expires_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_comment_votes_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_comment_votes_comment_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_comment_votes_vote_type"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_comment_votes_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_comments_author_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_comments_post_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_comments_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_comments_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_comments_parent_comment_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fishing_spots_name"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fishing_spots_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fishing_spots_location"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fishing_spots_created_by_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tags_name"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tags_category"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_post_votes_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_post_votes_post_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_post_votes_vote_type"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_post_votes_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_posts_author_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_posts_community_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_posts_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_posts_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_posts_upvotes"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_posts_views"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_community_members_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_community_members_community_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_community_members_role"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_community_members_joined_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_community_invites_community_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_community_invites_invited_by_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_community_invites_invited_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_community_invites_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_community_invites_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_communities_name"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_communities_visibility"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_communities_owner_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_user_follows_follower_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_user_follows_following_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_user_follows_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_users_email"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_users_uname"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_users_provider_accounts"`);
        await queryRunner.query(`ALTER TABLE "comment_votes" DROP CONSTRAINT "UQ_comment_votes_user_comment"`);
        await queryRunner.query(`ALTER TABLE "post_votes" DROP CONSTRAINT "UQ_post_votes_user_post"`);
        await queryRunner.query(`ALTER TABLE "community_members" DROP CONSTRAINT "UQ_community_members_user_community"`);
        await queryRunner.query(`ALTER TABLE "user_follows" DROP CONSTRAINT "UQ_user_follows_follower_following"`);
        await queryRunner.query(`ALTER TABLE "post_tags" RENAME COLUMN "tag_id" TO "tag_name"`);
        await queryRunner.query(`CREATE TABLE "fishing_spot_tags" ("fishing_spot_id" uuid NOT NULL, "tag_name" character varying(50) NOT NULL, CONSTRAINT "PK_423c2b8fc34559fe839508617a1" PRIMARY KEY ("fishing_spot_id", "tag_name"))`);
        await queryRunner.query(`CREATE INDEX "IDX_eff4d5931fab5ea0ebc4cc3b45" ON "fishing_spot_tags" ("fishing_spot_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_907e84d92158a3a809bffffd35" ON "fishing_spot_tags" ("tag_name") `);
        await queryRunner.query(`CREATE TABLE "post_fishing_spots" ("post_id" uuid NOT NULL, "fishing_spot_id" uuid NOT NULL, CONSTRAINT "PK_16e002cde4deb6095e8927c6e0b" PRIMARY KEY ("post_id", "fishing_spot_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_4ad47220384254999a8dba9dc4" ON "post_fishing_spots" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_818a4da5f62cf1844751f02eb2" ON "post_fishing_spots" ("fishing_spot_id") `);
        await queryRunner.query(`ALTER TABLE "tags" DROP CONSTRAINT "PK_tags"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP COLUMN "id"`);
        await queryRunner.query(`ALTER TABLE "posts" DROP COLUMN "fishing_spot_id"`);
        await queryRunner.query(`ALTER TABLE "comments" ADD "parentCommentId" uuid`);
        await queryRunner.query(`ALTER TABLE "tags" ADD "display_name" character varying(50) NOT NULL`);
        await queryRunner.query(`CREATE TYPE "public"."tags_status_enum" AS ENUM('Pending', 'Approved', 'Rejected')`);
        await queryRunner.query(`ALTER TABLE "tags" ADD "status" "public"."tags_status_enum" NOT NULL DEFAULT 'Pending'`);
        await queryRunner.query(`ALTER TABLE "tags" ADD "created_by" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tags" ADD "moderated_by" uuid`);
        await queryRunner.query(`ALTER TABLE "tags" ADD "moderated_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TYPE "public"."auth_provider_enum" RENAME TO "auth_provider_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."auth_tokens_provider_enum" AS ENUM('google', 'facebook', 'github', 'apple')`);
        await queryRunner.query(`ALTER TABLE "auth_tokens" ALTER COLUMN "provider" TYPE "public"."auth_tokens_provider_enum" USING "provider"::"text"::"public"."auth_tokens_provider_enum"`);
        await queryRunner.query(`DROP TYPE "public"."auth_provider_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."vote_type_enum" RENAME TO "vote_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."comment_votes_vote_type_enum" AS ENUM('Upvote', 'Downvote')`);
        await queryRunner.query(`ALTER TABLE "comment_votes" ALTER COLUMN "vote_type" TYPE "public"."comment_votes_vote_type_enum" USING "vote_type"::"text"::"public"."comment_votes_vote_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."vote_type_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."comment_status_enum" RENAME TO "comment_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."comments_status_enum" AS ENUM('Published', 'Deleted', 'Flagged', 'UnderReview')`);
        await queryRunner.query(`ALTER TABLE "comments" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "comments" ALTER COLUMN "status" TYPE "public"."comments_status_enum" USING "status"::"text"::"public"."comments_status_enum"`);
        await queryRunner.query(`ALTER TABLE "comments" ALTER COLUMN "status" SET DEFAULT 'Published'`);
        await queryRunner.query(`DROP TYPE "public"."comment_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP COLUMN "parent_comment_id"`);
        await queryRunner.query(`ALTER TABLE "comments" ADD "parent_comment_id" character varying`);
        await queryRunner.query(`ALTER TYPE "public"."fishing_spot_type_enum" RENAME TO "fishing_spot_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."fishing_spots_spot_type_enum" AS ENUM('Lake', 'River', 'Ocean', 'Pond', 'Stream', 'Bay', 'Reservoir', 'Other')`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ALTER COLUMN "spot_type" TYPE "public"."fishing_spots_spot_type_enum" USING "spot_type"::"text"::"public"."fishing_spots_spot_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."fishing_spot_type_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."fishing_spot_status_enum" RENAME TO "fishing_spot_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."fishing_spots_status_enum" AS ENUM('Active', 'Inactive', 'Private', 'Closed')`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ALTER COLUMN "status" TYPE "public"."fishing_spots_status_enum" USING "status"::"text"::"public"."fishing_spots_status_enum"`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ALTER COLUMN "status" SET DEFAULT 'Active'`);
        await queryRunner.query(`DROP TYPE "public"."fishing_spot_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "tags" ADD CONSTRAINT "PK_d90243459a697eadb8ad56e9092" PRIMARY KEY ("name")`);
        await queryRunner.query(`ALTER TABLE "tags" DROP CONSTRAINT "UQ_tags_name"`);
        await queryRunner.query(`ALTER TYPE "public"."vote_type_enum" RENAME TO "vote_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."post_votes_vote_type_enum" AS ENUM('Upvote', 'Downvote')`);
        await queryRunner.query(`ALTER TABLE "post_votes" ALTER COLUMN "vote_type" TYPE "public"."post_votes_vote_type_enum" USING "vote_type"::"text"::"public"."post_votes_vote_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."vote_type_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."post_status_enum" RENAME TO "post_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."posts_status_enum" AS ENUM('Draft', 'Published', 'Archived', 'Deleted', 'Flagged', 'UnderReview')`);
        await queryRunner.query(`ALTER TABLE "posts" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "posts" ALTER COLUMN "status" TYPE "public"."posts_status_enum" USING "status"::"text"::"public"."posts_status_enum"`);
        await queryRunner.query(`ALTER TABLE "posts" ALTER COLUMN "status" SET DEFAULT 'Published'`);
        await queryRunner.query(`DROP TYPE "public"."post_status_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."member_role_enum" RENAME TO "member_role_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."community_members_role_enum" AS ENUM('Member', 'Moderator', 'Admin')`);
        await queryRunner.query(`ALTER TABLE "community_members" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "community_members" ALTER COLUMN "role" TYPE "public"."community_members_role_enum" USING "role"::"text"::"public"."community_members_role_enum"`);
        await queryRunner.query(`ALTER TABLE "community_members" ALTER COLUMN "role" SET DEFAULT 'Member'`);
        await queryRunner.query(`DROP TYPE "public"."member_role_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."member_role_enum" RENAME TO "member_role_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."community_invites_role_enum" AS ENUM('Member', 'Moderator', 'Admin')`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "role" TYPE "public"."community_invites_role_enum" USING "role"::"text"::"public"."community_invites_role_enum"`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "role" SET DEFAULT 'Member'`);
        await queryRunner.query(`DROP TYPE "public"."member_role_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."invite_status_enum" RENAME TO "invite_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."community_invites_status_enum" AS ENUM('Pending', 'Accepted', 'Rejected')`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "status" TYPE "public"."community_invites_status_enum" USING "status"::"text"::"public"."community_invites_status_enum"`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "status" SET DEFAULT 'Pending'`);
        await queryRunner.query(`DROP TYPE "public"."invite_status_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."community_visibility_enum" RENAME TO "community_visibility_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."communities_visibility_enum" AS ENUM('Public', 'Private', 'Restricted')`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "visibility" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "visibility" TYPE "public"."communities_visibility_enum" USING "visibility"::"text"::"public"."communities_visibility_enum"`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "visibility" SET DEFAULT 'Public'`);
        await queryRunner.query(`DROP TYPE "public"."community_visibility_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."invite_permission_enum" RENAME TO "invite_permission_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."communities_invite_permission_enum" AS ENUM('Anyone', 'Members', 'Moderators', 'Admins')`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "invite_permission" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "invite_permission" TYPE "public"."communities_invite_permission_enum" USING "invite_permission"::"text"::"public"."communities_invite_permission_enum"`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "invite_permission" SET DEFAULT 'Moderators'`);
        await queryRunner.query(`DROP TYPE "public"."invite_permission_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."post_moderation_enum" RENAME TO "post_moderation_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."communities_post_moderation_enum" AS ENUM('None', 'PreModeration', 'PostModeration')`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "post_moderation" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "post_moderation" TYPE "public"."communities_post_moderation_enum" USING "post_moderation"::"text"::"public"."communities_post_moderation_enum"`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "post_moderation" SET DEFAULT 'None'`);
        await queryRunner.query(`DROP TYPE "public"."post_moderation_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."post_moderation_enum" RENAME TO "post_moderation_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."communities_comment_moderation_enum" AS ENUM('None', 'PreModeration', 'PostModeration')`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "comment_moderation" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "comment_moderation" TYPE "public"."communities_comment_moderation_enum" USING "comment_moderation"::"text"::"public"."communities_comment_moderation_enum"`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "comment_moderation" SET DEFAULT 'None'`);
        await queryRunner.query(`DROP TYPE "public"."post_moderation_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."user_status_enum" RENAME TO "user_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."users_status_enum" AS ENUM('Active', 'Inactive', 'Banned', 'Deleted')`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "status" TYPE "public"."users_status_enum" USING "status"::"text"::"public"."users_status_enum"`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "status" SET DEFAULT 'Active'`);
        await queryRunner.query(`DROP TYPE "public"."user_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP CONSTRAINT "PK_post_tags"`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD CONSTRAINT "PK_post_tags" PRIMARY KEY ("post_id")`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP COLUMN "tag_name"`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD "tag_name" character varying(50) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP CONSTRAINT "PK_post_tags"`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD CONSTRAINT "PK_post_tags" PRIMARY KEY ("post_id", "tag_name")`);
        await queryRunner.query(`CREATE INDEX "IDX_17b14e7b274ab102ed7699d8f1" ON "auth_tokens" ("provider_user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_9691367d446cd8b18f462c191b" ON "auth_tokens" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_f1a9342715c78e8614c548378c" ON "auth_tokens" ("provider", "provider_user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_e9f62f5dcb8a54b84234c9e7a0" ON "sessions" ("token") `);
        await queryRunner.query(`CREATE INDEX "IDX_9cfe37d28c3b229a350e086d94" ON "sessions" ("expires_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_9cfe37d28c3b229a350e086d94" ON "sessions" ("expires_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_085d540d9f418cfbdc7bd55bb1" ON "sessions" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_e9f62f5dcb8a54b84234c9e7a0" ON "sessions" ("token") `);
        await queryRunner.query(`CREATE INDEX "IDX_d9e5ec0c8307461dcf255bd7b0" ON "comment_votes" ("vote_type") `);
        await queryRunner.query(`CREATE INDEX "IDX_f4c4b5a4cb5e66494300469e78" ON "comment_votes" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_f4c4b5a4cb5e66494300469e78" ON "comment_votes" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_d9e5ec0c8307461dcf255bd7b0" ON "comment_votes" ("vote_type") `);
        await queryRunner.query(`CREATE INDEX "IDX_1b41b98c56a06654513bffc127" ON "comment_votes" ("comment_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_bc20ac5a0c8715d3e99e5dc679" ON "comment_votes" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_ca9691fe7256d3ef5d6626c165" ON "comments" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_93ce08bdbea73c0c7ee673ec35" ON "comments" ("parent_comment_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_8e7c9a36c0ac867b543c6509aa" ON "comments" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_93ce08bdbea73c0c7ee673ec35" ON "comments" ("parent_comment_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_8e7c9a36c0ac867b543c6509aa" ON "comments" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_ca9691fe7256d3ef5d6626c165" ON "comments" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_259bf9825d9d198608d1b46b0b" ON "comments" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_e6d38899c31997c45d128a8973" ON "comments" ("author_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_dd774f4a50eb85dc82a30fba05" ON "fishing_spots" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_534c8f96cd807ed98fb8a56840" ON "fishing_spots" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_8da949e4f83271dd0b6da8597c" ON "fishing_spots" ("location") `);
        await queryRunner.query(`CREATE INDEX "IDX_780e57db59fbe541c5758b6c1f" ON "fishing_spots" ("created_by_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_8da949e4f83271dd0b6da8597c" ON "fishing_spots" ("location") `);
        await queryRunner.query(`CREATE INDEX "IDX_534c8f96cd807ed98fb8a56840" ON "fishing_spots" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_dd774f4a50eb85dc82a30fba05" ON "fishing_spots" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_e66a4ffa4213d40bef0da5bc44" ON "tags" ("category") `);
        await queryRunner.query(`CREATE INDEX "IDX_b4ca77d1ea08b7644f67fb86ed" ON "tags" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_32f027a90ce9c91c9b8ff830d2" ON "tags" ("created_by") `);
        await queryRunner.query(`CREATE INDEX "IDX_e66a4ffa4213d40bef0da5bc44" ON "tags" ("category") `);
        await queryRunner.query(`CREATE INDEX "IDX_b4ca77d1ea08b7644f67fb86ed" ON "tags" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_aee3a82ed51c7f3c80cc540fa8" ON "post_votes" ("vote_type") `);
        await queryRunner.query(`CREATE INDEX "IDX_f983616aae0411cf4588cc98e3" ON "post_votes" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_f983616aae0411cf4588cc98e3" ON "post_votes" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_aee3a82ed51c7f3c80cc540fa8" ON "post_votes" ("vote_type") `);
        await queryRunner.query(`CREATE INDEX "IDX_be565a04e47c8b846f6cc7fcac" ON "post_votes" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_37fb39fa2d813582024968ef26" ON "post_votes" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_a69d9e2ae78ef7d100f8317ae0" ON "posts" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_0673895e03266a5765e5f36be6" ON "posts" ("upvotes") `);
        await queryRunner.query(`CREATE INDEX "IDX_2fb34da670a56b0fd349204213" ON "posts" ("views") `);
        await queryRunner.query(`CREATE INDEX "IDX_60818528127866f5002e7f826d" ON "posts" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_2fb34da670a56b0fd349204213" ON "posts" ("views") `);
        await queryRunner.query(`CREATE INDEX "IDX_0673895e03266a5765e5f36be6" ON "posts" ("upvotes") `);
        await queryRunner.query(`CREATE INDEX "IDX_60818528127866f5002e7f826d" ON "posts" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_a69d9e2ae78ef7d100f8317ae0" ON "posts" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_63078ada3266846e539d930b1b" ON "posts" ("community_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_312c63be865c81b922e39c2475" ON "posts" ("author_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_4c90c60ce9e4e02fd6f6f1a712" ON "community_members" ("role") `);
        await queryRunner.query(`CREATE INDEX "IDX_26cddc6cb97e51b1e492ef6c3e" ON "community_members" ("joined_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_26cddc6cb97e51b1e492ef6c3e" ON "community_members" ("joined_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_4c90c60ce9e4e02fd6f6f1a712" ON "community_members" ("role") `);
        await queryRunner.query(`CREATE INDEX "IDX_46eb2c3e2d8b84acbd9a78974a" ON "community_members" ("community_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_59ac0a0f039c16f8429ec9bda5" ON "community_members" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_ba9d8167553aef395f22712e6c" ON "community_invites" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_aac883f15dff7ce5bb4ce0ceff" ON "community_invites" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_aac883f15dff7ce5bb4ce0ceff" ON "community_invites" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_ba9d8167553aef395f22712e6c" ON "community_invites" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_935648d66d5fe93062497623b0" ON "community_invites" ("invited_user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_53d9d587f6b27c07e5eae5a875" ON "community_invites" ("invited_by_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_a49960e29dc423f42c58adab51" ON "community_invites" ("community_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_501bb6c8f7c8e8a7d614d9435f" ON "communities" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_850429b73a8409269d7092f476" ON "communities" ("visibility") `);
        await queryRunner.query(`CREATE INDEX "IDX_7e7174255c891094ede16398f0" ON "communities" ("owner_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_850429b73a8409269d7092f476" ON "communities" ("visibility") `);
        await queryRunner.query(`CREATE INDEX "IDX_501bb6c8f7c8e8a7d614d9435f" ON "communities" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_b340188b3e87d36026b99939c0" ON "user_follows" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_5a71643cec3110af425f92e76e" ON "user_follows" ("following_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_f7af3bf8f2dcba61b4adc10823" ON "user_follows" ("follower_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_2e487ae161f68af4d3dd67c5de" ON "users" ("uname") `);
        await queryRunner.query(`CREATE INDEX "IDX_97672ac88f789774dd47f7c8be" ON "users" ("email") `);
        await queryRunner.query(`CREATE INDEX "IDX_10050bcaeb411b18768ee7d9e0" ON "users" ("provider_accounts") `);
        await queryRunner.query(`CREATE INDEX "IDX_9e3a03f54aa7df34ac36cc251b" ON "users" ("provider_accounts") WHERE provider_accounts IS NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_2e487ae161f68af4d3dd67c5de" ON "users" ("uname") `);
        await queryRunner.query(`CREATE INDEX "IDX_97672ac88f789774dd47f7c8be" ON "users" ("email") `);
        await queryRunner.query(`CREATE INDEX "IDX_5df4e8dc2cb3e668b962362265" ON "post_tags" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_400e719fd738123e7bcdae675a" ON "post_tags" ("tag_name") `);
        await queryRunner.query(`CREATE INDEX "IDX_57e2f936281f847cc1ff3d85eb" ON "community_subscribers" ("community_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_3b46853378adc74b0e78f2615b" ON "community_subscribers" ("user_id") `);
        await queryRunner.query(`ALTER TABLE "comment_votes" ADD CONSTRAINT "UQ_134f68c8e62163194eb6cd95632" UNIQUE ("user_id", "comment_id")`);
        await queryRunner.query(`ALTER TABLE "post_votes" ADD CONSTRAINT "UQ_546801fe7213cc268591295ad83" UNIQUE ("user_id", "post_id")`);
        await queryRunner.query(`ALTER TABLE "community_members" ADD CONSTRAINT "UQ_a304f5a705ec45c9d11b5618287" UNIQUE ("user_id", "community_id")`);
        await queryRunner.query(`ALTER TABLE "user_follows" ADD CONSTRAINT "UQ_abc657d7ff1282910784b819171" UNIQUE ("follower_id", "following_id")`);
        await queryRunner.query(`ALTER TABLE "auth_tokens" ADD CONSTRAINT "FK_9691367d446cd8b18f462c191b3" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "sessions" ADD CONSTRAINT "FK_085d540d9f418cfbdc7bd55bb19" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comment_votes" ADD CONSTRAINT "FK_bc20ac5a0c8715d3e99e5dc6793" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comment_votes" ADD CONSTRAINT "FK_1b41b98c56a06654513bffc1274" FOREIGN KEY ("comment_id") REFERENCES "comments"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_e6d38899c31997c45d128a8973b" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_259bf9825d9d198608d1b46b0b5" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_4875672591221a61ace66f2d4f9" FOREIGN KEY ("parentCommentId") REFERENCES "comments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ADD CONSTRAINT "FK_780e57db59fbe541c5758b6c1f3" FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tags" ADD CONSTRAINT "FK_32f027a90ce9c91c9b8ff830d22" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tags" ADD CONSTRAINT "FK_49a133051ee8d8813afdbcb5bc9" FOREIGN KEY ("moderated_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "post_votes" ADD CONSTRAINT "FK_37fb39fa2d813582024968ef264" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "post_votes" ADD CONSTRAINT "FK_be565a04e47c8b846f6cc7fcac6" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "posts" ADD CONSTRAINT "FK_312c63be865c81b922e39c2475e" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "posts" ADD CONSTRAINT "FK_63078ada3266846e539d930b1be" FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_members" ADD CONSTRAINT "FK_59ac0a0f039c16f8429ec9bda5d" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_members" ADD CONSTRAINT "FK_46eb2c3e2d8b84acbd9a78974ab" FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_invites" ADD CONSTRAINT "FK_a49960e29dc423f42c58adab512" FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_invites" ADD CONSTRAINT "FK_53d9d587f6b27c07e5eae5a8758" FOREIGN KEY ("invited_by_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_invites" ADD CONSTRAINT "FK_935648d66d5fe93062497623b01" FOREIGN KEY ("invited_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "communities" ADD CONSTRAINT "FK_7e7174255c891094ede16398f02" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_follows" ADD CONSTRAINT "FK_f7af3bf8f2dcba61b4adc108239" FOREIGN KEY ("follower_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_follows" ADD CONSTRAINT "FK_5a71643cec3110af425f92e76e5" FOREIGN KEY ("following_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "fishing_spot_tags" ADD CONSTRAINT "FK_eff4d5931fab5ea0ebc4cc3b450" FOREIGN KEY ("fishing_spot_id") REFERENCES "fishing_spots"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "fishing_spot_tags" ADD CONSTRAINT "FK_907e84d92158a3a809bffffd35a" FOREIGN KEY ("tag_name") REFERENCES "tags"("name") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "post_fishing_spots" ADD CONSTRAINT "FK_4ad47220384254999a8dba9dc44" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "post_fishing_spots" ADD CONSTRAINT "FK_818a4da5f62cf1844751f02eb26" FOREIGN KEY ("fishing_spot_id") REFERENCES "fishing_spots"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD CONSTRAINT "FK_5df4e8dc2cb3e668b962362265d" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD CONSTRAINT "FK_400e719fd738123e7bcdae675a3" FOREIGN KEY ("tag_name") REFERENCES "tags"("name") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_subscribers" ADD CONSTRAINT "FK_57e2f936281f847cc1ff3d85ebe" FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "community_subscribers" ADD CONSTRAINT "FK_3b46853378adc74b0e78f2615bf" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "community_subscribers" DROP CONSTRAINT "FK_3b46853378adc74b0e78f2615bf"`);
        await queryRunner.query(`ALTER TABLE "community_subscribers" DROP CONSTRAINT "FK_57e2f936281f847cc1ff3d85ebe"`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP CONSTRAINT "FK_400e719fd738123e7bcdae675a3"`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP CONSTRAINT "FK_5df4e8dc2cb3e668b962362265d"`);
        await queryRunner.query(`ALTER TABLE "post_fishing_spots" DROP CONSTRAINT "FK_818a4da5f62cf1844751f02eb26"`);
        await queryRunner.query(`ALTER TABLE "post_fishing_spots" DROP CONSTRAINT "FK_4ad47220384254999a8dba9dc44"`);
        await queryRunner.query(`ALTER TABLE "fishing_spot_tags" DROP CONSTRAINT "FK_907e84d92158a3a809bffffd35a"`);
        await queryRunner.query(`ALTER TABLE "fishing_spot_tags" DROP CONSTRAINT "FK_eff4d5931fab5ea0ebc4cc3b450"`);
        await queryRunner.query(`ALTER TABLE "user_follows" DROP CONSTRAINT "FK_5a71643cec3110af425f92e76e5"`);
        await queryRunner.query(`ALTER TABLE "user_follows" DROP CONSTRAINT "FK_f7af3bf8f2dcba61b4adc108239"`);
        await queryRunner.query(`ALTER TABLE "communities" DROP CONSTRAINT "FK_7e7174255c891094ede16398f02"`);
        await queryRunner.query(`ALTER TABLE "community_invites" DROP CONSTRAINT "FK_935648d66d5fe93062497623b01"`);
        await queryRunner.query(`ALTER TABLE "community_invites" DROP CONSTRAINT "FK_53d9d587f6b27c07e5eae5a8758"`);
        await queryRunner.query(`ALTER TABLE "community_invites" DROP CONSTRAINT "FK_a49960e29dc423f42c58adab512"`);
        await queryRunner.query(`ALTER TABLE "community_members" DROP CONSTRAINT "FK_46eb2c3e2d8b84acbd9a78974ab"`);
        await queryRunner.query(`ALTER TABLE "community_members" DROP CONSTRAINT "FK_59ac0a0f039c16f8429ec9bda5d"`);
        await queryRunner.query(`ALTER TABLE "posts" DROP CONSTRAINT "FK_63078ada3266846e539d930b1be"`);
        await queryRunner.query(`ALTER TABLE "posts" DROP CONSTRAINT "FK_312c63be865c81b922e39c2475e"`);
        await queryRunner.query(`ALTER TABLE "post_votes" DROP CONSTRAINT "FK_be565a04e47c8b846f6cc7fcac6"`);
        await queryRunner.query(`ALTER TABLE "post_votes" DROP CONSTRAINT "FK_37fb39fa2d813582024968ef264"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP CONSTRAINT "FK_49a133051ee8d8813afdbcb5bc9"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP CONSTRAINT "FK_32f027a90ce9c91c9b8ff830d22"`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" DROP CONSTRAINT "FK_780e57db59fbe541c5758b6c1f3"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_4875672591221a61ace66f2d4f9"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_259bf9825d9d198608d1b46b0b5"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_e6d38899c31997c45d128a8973b"`);
        await queryRunner.query(`ALTER TABLE "comment_votes" DROP CONSTRAINT "FK_1b41b98c56a06654513bffc1274"`);
        await queryRunner.query(`ALTER TABLE "comment_votes" DROP CONSTRAINT "FK_bc20ac5a0c8715d3e99e5dc6793"`);
        await queryRunner.query(`ALTER TABLE "sessions" DROP CONSTRAINT "FK_085d540d9f418cfbdc7bd55bb19"`);
        await queryRunner.query(`ALTER TABLE "auth_tokens" DROP CONSTRAINT "FK_9691367d446cd8b18f462c191b3"`);
        await queryRunner.query(`ALTER TABLE "user_follows" DROP CONSTRAINT "UQ_abc657d7ff1282910784b819171"`);
        await queryRunner.query(`ALTER TABLE "community_members" DROP CONSTRAINT "UQ_a304f5a705ec45c9d11b5618287"`);
        await queryRunner.query(`ALTER TABLE "post_votes" DROP CONSTRAINT "UQ_546801fe7213cc268591295ad83"`);
        await queryRunner.query(`ALTER TABLE "comment_votes" DROP CONSTRAINT "UQ_134f68c8e62163194eb6cd95632"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3b46853378adc74b0e78f2615b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_57e2f936281f847cc1ff3d85eb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_400e719fd738123e7bcdae675a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5df4e8dc2cb3e668b962362265"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_97672ac88f789774dd47f7c8be"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2e487ae161f68af4d3dd67c5de"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9e3a03f54aa7df34ac36cc251b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_10050bcaeb411b18768ee7d9e0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_97672ac88f789774dd47f7c8be"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2e487ae161f68af4d3dd67c5de"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f7af3bf8f2dcba61b4adc10823"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5a71643cec3110af425f92e76e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b340188b3e87d36026b99939c0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_501bb6c8f7c8e8a7d614d9435f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_850429b73a8409269d7092f476"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7e7174255c891094ede16398f0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_850429b73a8409269d7092f476"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_501bb6c8f7c8e8a7d614d9435f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a49960e29dc423f42c58adab51"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_53d9d587f6b27c07e5eae5a875"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_935648d66d5fe93062497623b0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ba9d8167553aef395f22712e6c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_aac883f15dff7ce5bb4ce0ceff"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_aac883f15dff7ce5bb4ce0ceff"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ba9d8167553aef395f22712e6c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_59ac0a0f039c16f8429ec9bda5"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_46eb2c3e2d8b84acbd9a78974a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4c90c60ce9e4e02fd6f6f1a712"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_26cddc6cb97e51b1e492ef6c3e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_26cddc6cb97e51b1e492ef6c3e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4c90c60ce9e4e02fd6f6f1a712"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_312c63be865c81b922e39c2475"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_63078ada3266846e539d930b1b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a69d9e2ae78ef7d100f8317ae0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_60818528127866f5002e7f826d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0673895e03266a5765e5f36be6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2fb34da670a56b0fd349204213"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_60818528127866f5002e7f826d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2fb34da670a56b0fd349204213"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0673895e03266a5765e5f36be6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a69d9e2ae78ef7d100f8317ae0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_37fb39fa2d813582024968ef26"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_be565a04e47c8b846f6cc7fcac"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_aee3a82ed51c7f3c80cc540fa8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f983616aae0411cf4588cc98e3"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f983616aae0411cf4588cc98e3"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_aee3a82ed51c7f3c80cc540fa8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b4ca77d1ea08b7644f67fb86ed"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e66a4ffa4213d40bef0da5bc44"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_32f027a90ce9c91c9b8ff830d2"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b4ca77d1ea08b7644f67fb86ed"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e66a4ffa4213d40bef0da5bc44"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_dd774f4a50eb85dc82a30fba05"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_534c8f96cd807ed98fb8a56840"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8da949e4f83271dd0b6da8597c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_780e57db59fbe541c5758b6c1f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8da949e4f83271dd0b6da8597c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_534c8f96cd807ed98fb8a56840"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_dd774f4a50eb85dc82a30fba05"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e6d38899c31997c45d128a8973"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_259bf9825d9d198608d1b46b0b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ca9691fe7256d3ef5d6626c165"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8e7c9a36c0ac867b543c6509aa"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_93ce08bdbea73c0c7ee673ec35"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8e7c9a36c0ac867b543c6509aa"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_93ce08bdbea73c0c7ee673ec35"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ca9691fe7256d3ef5d6626c165"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_bc20ac5a0c8715d3e99e5dc679"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1b41b98c56a06654513bffc127"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d9e5ec0c8307461dcf255bd7b0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f4c4b5a4cb5e66494300469e78"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f4c4b5a4cb5e66494300469e78"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d9e5ec0c8307461dcf255bd7b0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e9f62f5dcb8a54b84234c9e7a0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_085d540d9f418cfbdc7bd55bb1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9cfe37d28c3b229a350e086d94"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9cfe37d28c3b229a350e086d94"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e9f62f5dcb8a54b84234c9e7a0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f1a9342715c78e8614c548378c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9691367d446cd8b18f462c191b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_17b14e7b274ab102ed7699d8f1"`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP CONSTRAINT "PK_post_tags"`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD CONSTRAINT "PK_post_tags" PRIMARY KEY ("post_id")`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP COLUMN "tag_name"`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD "tag_name" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "post_tags" DROP CONSTRAINT "PK_post_tags"`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD CONSTRAINT "PK_post_tags" PRIMARY KEY ("post_id", "tag_name")`);
        await queryRunner.query(`CREATE TYPE "public"."user_status_enum_old" AS ENUM('Active', 'Inactive', 'Banned', 'Deleted')`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "status" TYPE "public"."user_status_enum_old" USING "status"::"text"::"public"."user_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "status" SET DEFAULT 'Active'`);
        await queryRunner.query(`DROP TYPE "public"."users_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."user_status_enum_old" RENAME TO "user_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."post_moderation_enum_old" AS ENUM('None', 'PreModeration', 'PostModeration')`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "comment_moderation" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "comment_moderation" TYPE "public"."post_moderation_enum_old" USING "comment_moderation"::"text"::"public"."post_moderation_enum_old"`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "comment_moderation" SET DEFAULT 'None'`);
        await queryRunner.query(`DROP TYPE "public"."communities_comment_moderation_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."post_moderation_enum_old" RENAME TO "post_moderation_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."post_moderation_enum_old" AS ENUM('None', 'PreModeration', 'PostModeration')`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "post_moderation" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "post_moderation" TYPE "public"."post_moderation_enum_old" USING "post_moderation"::"text"::"public"."post_moderation_enum_old"`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "post_moderation" SET DEFAULT 'None'`);
        await queryRunner.query(`DROP TYPE "public"."communities_post_moderation_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."post_moderation_enum_old" RENAME TO "post_moderation_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."invite_permission_enum_old" AS ENUM('Anyone', 'Members', 'Moderators', 'Admins')`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "invite_permission" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "invite_permission" TYPE "public"."invite_permission_enum_old" USING "invite_permission"::"text"::"public"."invite_permission_enum_old"`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "invite_permission" SET DEFAULT 'Moderators'`);
        await queryRunner.query(`DROP TYPE "public"."communities_invite_permission_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."invite_permission_enum_old" RENAME TO "invite_permission_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."community_visibility_enum_old" AS ENUM('Public', 'Private', 'Restricted')`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "visibility" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "visibility" TYPE "public"."community_visibility_enum_old" USING "visibility"::"text"::"public"."community_visibility_enum_old"`);
        await queryRunner.query(`ALTER TABLE "communities" ALTER COLUMN "visibility" SET DEFAULT 'Public'`);
        await queryRunner.query(`DROP TYPE "public"."communities_visibility_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."community_visibility_enum_old" RENAME TO "community_visibility_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."invite_status_enum_old" AS ENUM('Pending', 'Accepted', 'Rejected')`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "status" TYPE "public"."invite_status_enum_old" USING "status"::"text"::"public"."invite_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "status" SET DEFAULT 'Pending'`);
        await queryRunner.query(`DROP TYPE "public"."community_invites_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."invite_status_enum_old" RENAME TO "invite_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."member_role_enum_old" AS ENUM('Member', 'Moderator', 'Admin')`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "role" TYPE "public"."member_role_enum_old" USING "role"::"text"::"public"."member_role_enum_old"`);
        await queryRunner.query(`ALTER TABLE "community_invites" ALTER COLUMN "role" SET DEFAULT 'Member'`);
        await queryRunner.query(`DROP TYPE "public"."community_invites_role_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."member_role_enum_old" RENAME TO "member_role_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."member_role_enum_old" AS ENUM('Member', 'Moderator', 'Admin')`);
        await queryRunner.query(`ALTER TABLE "community_members" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "community_members" ALTER COLUMN "role" TYPE "public"."member_role_enum_old" USING "role"::"text"::"public"."member_role_enum_old"`);
        await queryRunner.query(`ALTER TABLE "community_members" ALTER COLUMN "role" SET DEFAULT 'Member'`);
        await queryRunner.query(`DROP TYPE "public"."community_members_role_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."member_role_enum_old" RENAME TO "member_role_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."post_status_enum_old" AS ENUM('Draft', 'Published', 'Archived', 'Deleted', 'Flagged', 'UnderReview')`);
        await queryRunner.query(`ALTER TABLE "posts" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "posts" ALTER COLUMN "status" TYPE "public"."post_status_enum_old" USING "status"::"text"::"public"."post_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "posts" ALTER COLUMN "status" SET DEFAULT 'Published'`);
        await queryRunner.query(`DROP TYPE "public"."posts_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."post_status_enum_old" RENAME TO "post_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."vote_type_enum_old" AS ENUM('Upvote', 'Downvote')`);
        await queryRunner.query(`ALTER TABLE "post_votes" ALTER COLUMN "vote_type" TYPE "public"."vote_type_enum_old" USING "vote_type"::"text"::"public"."vote_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."post_votes_vote_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."vote_type_enum_old" RENAME TO "vote_type_enum"`);
        await queryRunner.query(`ALTER TABLE "tags" ADD CONSTRAINT "UQ_tags_name" UNIQUE ("name")`);
        await queryRunner.query(`ALTER TABLE "tags" DROP CONSTRAINT "PK_d90243459a697eadb8ad56e9092"`);
        await queryRunner.query(`CREATE TYPE "public"."fishing_spot_status_enum_old" AS ENUM('Active', 'Inactive', 'Private', 'Closed')`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ALTER COLUMN "status" TYPE "public"."fishing_spot_status_enum_old" USING "status"::"text"::"public"."fishing_spot_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ALTER COLUMN "status" SET DEFAULT 'Active'`);
        await queryRunner.query(`DROP TYPE "public"."fishing_spots_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."fishing_spot_status_enum_old" RENAME TO "fishing_spot_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."fishing_spot_type_enum_old" AS ENUM('Lake', 'River', 'Ocean', 'Pond', 'Stream', 'Bay', 'Reservoir', 'Other')`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ALTER COLUMN "spot_type" TYPE "public"."fishing_spot_type_enum_old" USING "spot_type"::"text"::"public"."fishing_spot_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."fishing_spots_spot_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."fishing_spot_type_enum_old" RENAME TO "fishing_spot_type_enum"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP COLUMN "parent_comment_id"`);
        await queryRunner.query(`ALTER TABLE "comments" ADD "parent_comment_id" uuid`);
        await queryRunner.query(`CREATE TYPE "public"."comment_status_enum_old" AS ENUM('Published', 'Deleted', 'Flagged', 'UnderReview')`);
        await queryRunner.query(`ALTER TABLE "comments" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "comments" ALTER COLUMN "status" TYPE "public"."comment_status_enum_old" USING "status"::"text"::"public"."comment_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "comments" ALTER COLUMN "status" SET DEFAULT 'Published'`);
        await queryRunner.query(`DROP TYPE "public"."comments_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."comment_status_enum_old" RENAME TO "comment_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."vote_type_enum_old" AS ENUM('Upvote', 'Downvote')`);
        await queryRunner.query(`ALTER TABLE "comment_votes" ALTER COLUMN "vote_type" TYPE "public"."vote_type_enum_old" USING "vote_type"::"text"::"public"."vote_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."comment_votes_vote_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."vote_type_enum_old" RENAME TO "vote_type_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."auth_provider_enum_old" AS ENUM('google', 'facebook', 'github', 'apple')`);
        await queryRunner.query(`ALTER TABLE "auth_tokens" ALTER COLUMN "provider" TYPE "public"."auth_provider_enum_old" USING "provider"::"text"::"public"."auth_provider_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."auth_tokens_provider_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."auth_provider_enum_old" RENAME TO "auth_provider_enum"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP COLUMN "moderated_at"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP COLUMN "moderated_by"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."tags_status_enum"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP COLUMN "display_name"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP COLUMN "parentCommentId"`);
        await queryRunner.query(`ALTER TABLE "posts" ADD "fishing_spot_id" uuid`);
        await queryRunner.query(`ALTER TABLE "tags" ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()`);
        await queryRunner.query(`ALTER TABLE "tags" ADD CONSTRAINT "PK_tags" PRIMARY KEY ("id")`);
        await queryRunner.query(`DROP INDEX "public"."IDX_818a4da5f62cf1844751f02eb2"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4ad47220384254999a8dba9dc4"`);
        await queryRunner.query(`DROP TABLE "post_fishing_spots"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_907e84d92158a3a809bffffd35"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_eff4d5931fab5ea0ebc4cc3b45"`);
        await queryRunner.query(`DROP TABLE "fishing_spot_tags"`);
        await queryRunner.query(`ALTER TABLE "post_tags" RENAME COLUMN "tag_name" TO "tag_id"`);
        await queryRunner.query(`ALTER TABLE "user_follows" ADD CONSTRAINT "UQ_user_follows_follower_following" UNIQUE ("follower_id", "following_id")`);
        await queryRunner.query(`ALTER TABLE "community_members" ADD CONSTRAINT "UQ_community_members_user_community" UNIQUE ("user_id", "community_id")`);
        await queryRunner.query(`ALTER TABLE "post_votes" ADD CONSTRAINT "UQ_post_votes_user_post" UNIQUE ("user_id", "post_id")`);
        await queryRunner.query(`ALTER TABLE "comment_votes" ADD CONSTRAINT "UQ_comment_votes_user_comment" UNIQUE ("user_id", "comment_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_users_provider_accounts" ON "users" ("provider_accounts") `);
        await queryRunner.query(`CREATE INDEX "IDX_users_uname" ON "users" ("uname") `);
        await queryRunner.query(`CREATE INDEX "IDX_users_email" ON "users" ("email") `);
        await queryRunner.query(`CREATE INDEX "IDX_user_follows_created_at" ON "user_follows" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_user_follows_following_id" ON "user_follows" ("following_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_user_follows_follower_id" ON "user_follows" ("follower_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_communities_owner_id" ON "communities" ("owner_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_communities_visibility" ON "communities" ("visibility") `);
        await queryRunner.query(`CREATE INDEX "IDX_communities_name" ON "communities" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_community_invites_created_at" ON "community_invites" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_community_invites_status" ON "community_invites" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_community_invites_invited_user_id" ON "community_invites" ("invited_user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_community_invites_invited_by_id" ON "community_invites" ("invited_by_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_community_invites_community_id" ON "community_invites" ("community_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_community_members_joined_at" ON "community_members" ("joined_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_community_members_role" ON "community_members" ("role") `);
        await queryRunner.query(`CREATE INDEX "IDX_community_members_community_id" ON "community_members" ("community_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_community_members_user_id" ON "community_members" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_posts_views" ON "posts" ("views") `);
        await queryRunner.query(`CREATE INDEX "IDX_posts_upvotes" ON "posts" ("upvotes") `);
        await queryRunner.query(`CREATE INDEX "IDX_posts_created_at" ON "posts" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_posts_status" ON "posts" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_posts_community_id" ON "posts" ("community_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_posts_author_id" ON "posts" ("author_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_post_votes_created_at" ON "post_votes" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_post_votes_vote_type" ON "post_votes" ("vote_type") `);
        await queryRunner.query(`CREATE INDEX "IDX_post_votes_post_id" ON "post_votes" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_post_votes_user_id" ON "post_votes" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_tags_category" ON "tags" ("category") `);
        await queryRunner.query(`CREATE INDEX "IDX_tags_name" ON "tags" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_fishing_spots_created_by_id" ON "fishing_spots" ("created_by_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_fishing_spots_location" ON "fishing_spots" ("location") `);
        await queryRunner.query(`CREATE INDEX "IDX_fishing_spots_status" ON "fishing_spots" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_fishing_spots_name" ON "fishing_spots" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_comments_parent_comment_id" ON "comments" ("parent_comment_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_comments_created_at" ON "comments" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_comments_status" ON "comments" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_comments_post_id" ON "comments" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_comments_author_id" ON "comments" ("author_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_comment_votes_created_at" ON "comment_votes" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_comment_votes_vote_type" ON "comment_votes" ("vote_type") `);
        await queryRunner.query(`CREATE INDEX "IDX_comment_votes_comment_id" ON "comment_votes" ("comment_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_comment_votes_user_id" ON "comment_votes" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_sessions_expires_at" ON "sessions" ("expires_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_sessions_token" ON "sessions" ("token") `);
        await queryRunner.query(`CREATE INDEX "IDX_sessions_user_id" ON "sessions" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_auth_tokens_provider_user" ON "auth_tokens" ("provider", "provider_user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_auth_tokens_user_id" ON "auth_tokens" ("user_id") `);
        await queryRunner.query(`ALTER TABLE "community_subscribers" ADD CONSTRAINT "FK_community_subscribers_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_subscribers" ADD CONSTRAINT "FK_community_subscribers_community" FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD CONSTRAINT "FK_post_tags_tag" FOREIGN KEY ("tag_id") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "post_tags" ADD CONSTRAINT "FK_post_tags_post" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_follows" ADD CONSTRAINT "FK_user_follows_following" FOREIGN KEY ("following_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_follows" ADD CONSTRAINT "FK_user_follows_follower" FOREIGN KEY ("follower_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "communities" ADD CONSTRAINT "FK_communities_owner" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_invites" ADD CONSTRAINT "FK_community_invites_invited_user" FOREIGN KEY ("invited_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_invites" ADD CONSTRAINT "FK_community_invites_invited_by" FOREIGN KEY ("invited_by_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_invites" ADD CONSTRAINT "FK_community_invites_community" FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_members" ADD CONSTRAINT "FK_community_members_community" FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "community_members" ADD CONSTRAINT "FK_community_members_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "posts" ADD CONSTRAINT "FK_posts_fishing_spot" FOREIGN KEY ("fishing_spot_id") REFERENCES "fishing_spots"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "posts" ADD CONSTRAINT "FK_posts_community" FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "posts" ADD CONSTRAINT "FK_posts_author" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "post_votes" ADD CONSTRAINT "FK_post_votes_post" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "post_votes" ADD CONSTRAINT "FK_post_votes_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "fishing_spots" ADD CONSTRAINT "FK_fishing_spots_created_by" FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_comments_parent" FOREIGN KEY ("parent_comment_id") REFERENCES "comments"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_comments_post" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_comments_author" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comment_votes" ADD CONSTRAINT "FK_comment_votes_comment" FOREIGN KEY ("comment_id") REFERENCES "comments"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comment_votes" ADD CONSTRAINT "FK_comment_votes_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "sessions" ADD CONSTRAINT "FK_sessions_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auth_tokens" ADD CONSTRAINT "FK_auth_tokens_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
