import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike, In } from 'typeorm';
import {
  CommunityEntity,
  CommunityMemberEntity,
  CommunityInviteEntity,
  UserEntity
} from '../entities';
import {
  Community,
  CommunityMember,
  CommunityInvite,
  User,
  CommunityVisibility,
  MemberRole
} from '@/types/global';

@Injectable()
export class CommunityRepository {
  private readonly logger = new Logger(CommunityRepository.name);

  constructor(
    @InjectRepository(CommunityEntity)
    private communityRepository: Repository<CommunityEntity>,
    @InjectRepository(CommunityMemberEntity)
    private memberRepository: Repository<CommunityMemberEntity>,
    @InjectRepository(CommunityInviteEntity)
    private inviteRepository: Repository<CommunityInviteEntity>,
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
  ) { }

  // Helper method to convert entity to type
  private entityToCommunity (entity: CommunityEntity): Community {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      rules: entity.rules,
      visibility: entity.visibility,
      invitePermission: entity.invitePermission,
      postModeration: entity.postModeration,
      commentModeration: entity.commentModeration,
      pic: entity.pic,
      banner: entity.banner,
      owner: entity.owner ? this.entityToUser(entity.owner) : null,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToCommunityMember (entity: CommunityMemberEntity): CommunityMember {
    return {
      id: entity.id,
      user: entity.user ? this.entityToUser(entity.user) : null,
      community: entity.community ? this.entityToCommunity(entity.community) : null,
      role: entity.role,
      joinedAt: entity.joinedAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToCommunityInvite (entity: CommunityInviteEntity): CommunityInvite {
    return {
      id: entity.id,
      community: entity.community ? this.entityToCommunity(entity.community) : null,
      invitedBy: entity.invitedBy ? this.entityToUser(entity.invitedBy) : null,
      invitedUser: entity.invitedUser ? this.entityToUser(entity.invitedUser) : null,
      role: entity.role,
      status: entity.status,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToUser (entity: UserEntity): User {
    return {
      id: entity.id,
      uname: entity.uname,
      status: entity.status,
      email: entity.email,
      emailVerified: entity.emailVerified,
      profile: entity.profile,
      prefs: entity.prefs,
      activity: entity.activity,
      savedContent: entity.savedContent,
      subscribedCommunities: [],
      providerAccounts: entity.providerAccounts || {},
      lastLoginAt: entity.lastLoginAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  // Community CRUD operations
  async createCommunity (communityData: Partial<CommunityEntity>): Promise<Community> {
    try {
      const community = this.communityRepository.create(communityData);
      const savedCommunity = await this.communityRepository.save(community);

      // Load the community with owner relation
      const communityWithOwner = await this.communityRepository.findOne({
        where: { id: savedCommunity.id },
        relations: ['owner'],
      });

      this.logger.log(`Created community: ${savedCommunity.name} (${savedCommunity.id})`);
      return this.entityToCommunity(communityWithOwner);
    } catch (error) {
      this.logger.error(`Failed to create community: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findCommunityById (id: string): Promise<Community | null> {
    try {
      const community = await this.communityRepository.findOne({
        where: { id },
        relations: ['owner'],
      });

      return community ? this.entityToCommunity(community) : null;
    } catch (error) {
      this.logger.error(`Failed to find community by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findCommunityByName (name: string): Promise<Community | null> {
    try {
      const community = await this.communityRepository.findOne({
        where: { name },
        relations: ['owner'],
      });

      return community ? this.entityToCommunity(community) : null;
    } catch (error) {
      this.logger.error(`Failed to find community by name ${name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateCommunity (id: string, updateData: Partial<CommunityEntity>): Promise<Community | null> {
    try {
      await this.communityRepository.update(id, updateData);
      return this.findCommunityById(id);
    } catch (error) {
      this.logger.error(`Failed to update community ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteCommunity (id: string): Promise<boolean> {
    try {
      const result = await this.communityRepository.delete(id);
      const deleted = result.affected > 0;

      if (deleted) {
        this.logger.log(`Deleted community: ${id}`);
      }

      return deleted;
    } catch (error) {
      this.logger.error(`Failed to delete community ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Community listing and search
  async findPublicCommunities (
    page: number = 1,
    limit: number = 20,
    search?: string
  ): Promise<{ communities: Community[]; total: number }> {
    try {
      const queryBuilder = this.communityRepository
        .createQueryBuilder('community')
        .leftJoinAndSelect('community.owner', 'owner')
        .where('community.visibility = :visibility', { visibility: CommunityVisibility.Public });

      if (search) {
        queryBuilder.andWhere(
          '(community.name ILIKE :search OR community.description ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      queryBuilder
        .orderBy('community.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [entities, total] = await queryBuilder.getManyAndCount();
      const communities = entities.map(entity => this.entityToCommunity(entity));

      return { communities, total };
    } catch (error) {
      this.logger.error(`Failed to find public communities: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Community membership operations
  async addCommunityMember (
    communityId: string,
    userId: string,
    role: MemberRole = MemberRole.Member
  ): Promise<CommunityMember> {
    try {
      const member = this.memberRepository.create({
        communityId,
        userId,
        role,
      });

      const savedMember = await this.memberRepository.save(member);

      // Load the member with relations
      const memberWithRelations = await this.memberRepository.findOne({
        where: { id: savedMember.id },
        relations: ['user', 'community', 'community.owner'],
      });

      this.logger.log(`Added member ${userId} to community ${communityId} with role ${role}`);
      return this.entityToCommunityMember(memberWithRelations);
    } catch (error) {
      this.logger.error(`Failed to add member to community: ${error.message}`, error.stack);
      throw error;
    }
  }

  async removeCommunityMember (communityId: string, userId: string): Promise<boolean> {
    try {
      const result = await this.memberRepository.delete({
        communityId,
        userId,
      });

      const removed = result.affected > 0;

      if (removed) {
        this.logger.log(`Removed member ${userId} from community ${communityId}`);
      }

      return removed;
    } catch (error) {
      this.logger.error(`Failed to remove member from community: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateMemberRole (
    communityId: string,
    userId: string,
    role: MemberRole
  ): Promise<CommunityMember | null> {
    try {
      await this.memberRepository.update(
        { communityId, userId },
        { role }
      );

      const member = await this.memberRepository.findOne({
        where: { communityId, userId },
        relations: ['user', 'community', 'community.owner'],
      });

      if (member) {
        this.logger.log(`Updated member ${userId} role to ${role} in community ${communityId}`);
        return this.entityToCommunityMember(member);
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to update member role: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCommunityMembers (
    communityId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ members: CommunityMember[]; total: number }> {
    try {
      const [entities, total] = await this.memberRepository.findAndCount({
        where: { communityId },
        relations: ['user', 'community', 'community.owner'],
        order: { joinedAt: 'ASC' },
        skip: (page - 1) * limit,
        take: limit,
      });

      const members = entities.map(entity => this.entityToCommunityMember(entity));
      return { members, total };
    } catch (error) {
      this.logger.error(`Failed to get community members: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getUserMembership (communityId: string, userId: string): Promise<CommunityMember | null> {
    try {
      const member = await this.memberRepository.findOne({
        where: { communityId, userId },
        relations: ['user', 'community', 'community.owner'],
      });

      return member ? this.entityToCommunityMember(member) : null;
    } catch (error) {
      this.logger.error(`Failed to get user membership: ${error.message}`, error.stack);
      throw error;
    }
  }

  async isUserMember (communityId: string, userId: string): Promise<boolean> {
    try {
      const count = await this.memberRepository.count({
        where: { communityId, userId },
      });

      return count > 0;
    } catch (error) {
      this.logger.error(`Failed to check user membership: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getUserCommunities (
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ communities: Community[]; total: number }> {
    try {
      const [memberEntities, total] = await this.memberRepository.findAndCount({
        where: { userId },
        relations: ['community', 'community.owner'],
        order: { joinedAt: 'DESC' },
        skip: (page - 1) * limit,
        take: limit,
      });

      const communities = memberEntities.map(member => this.entityToCommunity(member.community));
      return { communities, total };
    } catch (error) {
      this.logger.error(`Failed to get user communities: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Community invitation operations
  async createCommunityInvite (
    communityId: string,
    invitedById: string,
    invitedUserId: string,
    role: MemberRole = MemberRole.Member
  ): Promise<CommunityInvite> {
    try {
      const invite = this.inviteRepository.create({
        communityId,
        invitedById,
        invitedUserId,
        role,
        status: 'Pending',
      });

      const savedInvite = await this.inviteRepository.save(invite);

      // Load the invite with relations
      const inviteWithRelations = await this.inviteRepository.findOne({
        where: { id: savedInvite.id },
        relations: ['community', 'community.owner', 'invitedBy', 'invitedUser'],
      });

      this.logger.log(`Created invite for user ${invitedUserId} to community ${communityId}`);
      return this.entityToCommunityInvite(inviteWithRelations);
    } catch (error) {
      this.logger.error(`Failed to create community invite: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCommunityInvites (
    communityId: string,
    status?: 'Pending' | 'Accepted' | 'Rejected',
    page: number = 1,
    limit: number = 20
  ): Promise<{ invites: CommunityInvite[]; total: number }> {
    try {
      const whereCondition: any = { communityId };
      if (status) {
        whereCondition.status = status;
      }

      const [entities, total] = await this.inviteRepository.findAndCount({
        where: whereCondition,
        relations: ['community', 'community.owner', 'invitedBy', 'invitedUser'],
        order: { createdAt: 'DESC' },
        skip: (page - 1) * limit,
        take: limit,
      });

      const invites = entities.map(entity => this.entityToCommunityInvite(entity));
      return { invites, total };
    } catch (error) {
      this.logger.error(`Failed to get community invites: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getUserInvites (
    userId: string,
    status?: 'Pending' | 'Accepted' | 'Rejected',
    page: number = 1,
    limit: number = 20
  ): Promise<{ invites: CommunityInvite[]; total: number }> {
    try {
      const whereCondition: any = { invitedUserId: userId };
      if (status) {
        whereCondition.status = status;
      }

      const [entities, total] = await this.inviteRepository.findAndCount({
        where: whereCondition,
        relations: ['community', 'community.owner', 'invitedBy', 'invitedUser'],
        order: { createdAt: 'DESC' },
        skip: (page - 1) * limit,
        take: limit,
      });

      const invites = entities.map(entity => this.entityToCommunityInvite(entity));
      return { invites, total };
    } catch (error) {
      this.logger.error(`Failed to get user invites: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateInviteStatus (
    inviteId: string,
    status: 'Pending' | 'Accepted' | 'Rejected'
  ): Promise<CommunityInvite | null> {
    try {
      await this.inviteRepository.update(inviteId, { status });

      const invite = await this.inviteRepository.findOne({
        where: { id: inviteId },
        relations: ['community', 'community.owner', 'invitedBy', 'invitedUser'],
      });

      if (invite) {
        this.logger.log(`Updated invite ${inviteId} status to ${status}`);
        return this.entityToCommunityInvite(invite);
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to update invite status: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteInvite (inviteId: string): Promise<boolean> {
    try {
      const result = await this.inviteRepository.delete(inviteId);
      const deleted = result.affected > 0;

      if (deleted) {
        this.logger.log(`Deleted invite: ${inviteId}`);
      }

      return deleted;
    } catch (error) {
      this.logger.error(`Failed to delete invite: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findInviteById (inviteId: string): Promise<CommunityInvite | null> {
    try {
      const invite = await this.inviteRepository.findOne({
        where: { id: inviteId },
        relations: ['community', 'community.owner', 'invitedBy', 'invitedUser'],
      });

      return invite ? this.entityToCommunityInvite(invite) : null;
    } catch (error) {
      this.logger.error(`Failed to find invite by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  async hasExistingInvite (communityId: string, invitedUserId: string): Promise<boolean> {
    try {
      const count = await this.inviteRepository.count({
        where: {
          communityId,
          invitedUserId,
          status: 'Pending',
        },
      });

      return count > 0;
    } catch (error) {
      this.logger.error(`Failed to check existing invite: ${error.message}`, error.stack);
      throw error;
    }
  }
}
