import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { TagEntity } from '../entities/tag.entity';
import { Tag, TagStatus } from '@/types/global';
import { TagSearchQueryDto } from '@/tags/dto/tags.dto';

@Injectable()
export class TagRepository {
  private readonly logger = new Logger(TagRepository.name);

  constructor(
    @InjectRepository(TagEntity)
    private readonly tagRepository: Repository<TagEntity>,
  ) { }

  // Convert entity to domain type
  private entityToTag (entity: TagEntity): Tag {
    return {
      name: entity.name,
      displayName: entity.displayName,
      description: entity.description,
      category: entity.category,
      color: entity.color,
      status: entity.status,
      usageCount: entity.usageCount,
      createdBy: entity.creator ? {
        id: entity.creator.id,
        uname: entity.creator.uname,
        status: entity.creator.status,
        email: entity.creator.email,
        emailVerified: entity.creator.emailVerified,
        profile: entity.creator.profile,
        prefs: entity.creator.prefs,
        activity: entity.creator.activity,
        savedContent: entity.creator.savedContent,
        subscribedCommunities: [], // Will be populated separately if needed
        providerAccounts: entity.creator.providerAccounts,
        lastLoginAt: entity.creator.lastLoginAt,
        createdAt: entity.creator.createdAt,
        updatedAt: entity.creator.updatedAt,
      } : null,
      moderatedBy: entity.moderator ? {
        id: entity.moderator.id,
        uname: entity.moderator.uname,
        status: entity.moderator.status,
        email: entity.moderator.email,
        emailVerified: entity.moderator.emailVerified,
        profile: entity.moderator.profile,
        prefs: entity.moderator.prefs,
        activity: entity.moderator.activity,
        savedContent: entity.moderator.savedContent,
        subscribedCommunities: [], // Will be populated separately if needed
        providerAccounts: entity.moderator.providerAccounts,
        lastLoginAt: entity.moderator.lastLoginAt,
        createdAt: entity.moderator.createdAt,
        updatedAt: entity.moderator.updatedAt,
      } : null,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      moderatedAt: entity.moderatedAt,
    };
  }

  // Tag CRUD operations
  async createTag (tagData: Partial<TagEntity>): Promise<Tag> {
    try {
      const tag = this.tagRepository.create(tagData);
      const savedTag = await this.tagRepository.save(tag);

      // Load the tag with relations
      const tagWithRelations = await this.tagRepository.findOne({
        where: { name: savedTag.name },
        relations: ['creator', 'moderator'],
      });

      this.logger.log(`Created tag: ${savedTag.name}`);
      return this.entityToTag(tagWithRelations);
    } catch (error) {
      this.logger.error(`Failed to create tag: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findTagByName (name: string): Promise<Tag | null> {
    try {
      const tag = await this.tagRepository.findOne({
        where: { name },
        relations: ['creator', 'moderator'],
      });

      return tag ? this.entityToTag(tag) : null;
    } catch (error) {
      this.logger.error(`Failed to find tag ${name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findTagsByNames (names: string[]): Promise<Tag[]> {
    try {
      if (names.length === 0) return [];

      const tags = await this.tagRepository.find({
        where: names.map(name => ({ name })),
        relations: ['creator', 'moderator'],
      });

      return tags.map(tag => this.entityToTag(tag));
    } catch (error) {
      this.logger.error(`Failed to find tags: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateTag (name: string, updateData: Partial<TagEntity>): Promise<Tag> {
    try {
      await this.tagRepository.update({ name }, {
        ...updateData,
        updatedAt: new Date(),
      });

      const updatedTag = await this.tagRepository.findOne({
        where: { name },
        relations: ['creator', 'moderator'],
      });

      if (!updatedTag) {
        throw new Error('Tag not found after update');
      }

      this.logger.log(`Updated tag: ${name}`);
      return this.entityToTag(updatedTag);
    } catch (error) {
      this.logger.error(`Failed to update tag ${name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteTag (name: string): Promise<void> {
    try {
      const result = await this.tagRepository.delete({ name });

      if (result.affected === 0) {
        throw new Error('Tag not found');
      }

      this.logger.log(`Deleted tag: ${name}`);
    } catch (error) {
      this.logger.error(`Failed to delete tag ${name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Tag search and listing
  async searchTags (query: TagSearchQueryDto): Promise<{ tags: Tag[]; total: number }> {
    try {
      const queryBuilder = this.createSearchQueryBuilder(query);

      // Get total count
      const total = await queryBuilder.getCount();

      // Apply pagination and get results
      const tags = await queryBuilder
        .skip(query.offset || 0)
        .take(query.limit || 20)
        .leftJoinAndSelect('tag.creator', 'creator')
        .leftJoinAndSelect('tag.moderator', 'moderator')
        .getMany();

      return {
        tags: tags.map(tag => this.entityToTag(tag)),
        total,
      };
    } catch (error) {
      this.logger.error(`Failed to search tags: ${error.message}`, error.stack);
      throw error;
    }
  }

  private createSearchQueryBuilder (query: TagSearchQueryDto): SelectQueryBuilder<TagEntity> {
    const queryBuilder = this.tagRepository.createQueryBuilder('tag');

    // Search filter
    if (query.search) {
      queryBuilder.andWhere(
        '(tag.name ILIKE :search OR tag.displayName ILIKE :search OR tag.description ILIKE :search)',
        { search: `%${query.search}%` }
      );
    }

    // Category filter
    if (query.category) {
      queryBuilder.andWhere('tag.category = :category', { category: query.category });
    }

    // Status filter
    if (query.status) {
      queryBuilder.andWhere('tag.status = :status', { status: query.status });
    }

    // Sorting
    const sortBy = query.sortBy || 'usageCount';
    const sortOrder = query.sortOrder || 'DESC';

    if (sortBy === 'name') {
      queryBuilder.orderBy('tag.name', sortOrder);
    } else if (sortBy === 'usageCount') {
      queryBuilder.orderBy('tag.usageCount', sortOrder);
    } else if (sortBy === 'createdAt') {
      queryBuilder.orderBy('tag.createdAt', sortOrder);
    } else if (sortBy === 'updatedAt') {
      queryBuilder.orderBy('tag.updatedAt', sortOrder);
    }

    return queryBuilder;
  }

  // Tag moderation
  async moderateTag (name: string, status: TagStatus, moderatorId: string): Promise<Tag> {
    try {
      await this.tagRepository.update({ name }, {
        status,
        moderatedBy: moderatorId,
        moderatedAt: new Date(),
        updatedAt: new Date(),
      });

      const moderatedTag = await this.tagRepository.findOne({
        where: { name },
        relations: ['creator', 'moderator'],
      });

      if (!moderatedTag) {
        throw new Error('Tag not found after moderation');
      }

      this.logger.log(`Moderated tag ${name} to status: ${status}`);
      return this.entityToTag(moderatedTag);
    } catch (error) {
      this.logger.error(`Failed to moderate tag ${name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Tag statistics
  async getTagStats (): Promise<{
    totalTags: number;
    approvedTags: number;
    pendingTags: number;
    rejectedTags: number;
    categoryCounts: Record<string, number>;
  }> {
    try {
      const [totalTags, approvedTags, pendingTags, rejectedTags] = await Promise.all([
        this.tagRepository.count(),
        this.tagRepository.count({ where: { status: TagStatus.Approved } }),
        this.tagRepository.count({ where: { status: TagStatus.Pending } }),
        this.tagRepository.count({ where: { status: TagStatus.Rejected } }),
      ]);

      // Get category counts
      const categoryResults = await this.tagRepository
        .createQueryBuilder('tag')
        .select('tag.category', 'category')
        .addSelect('COUNT(*)', 'count')
        .where('tag.category IS NOT NULL')
        .groupBy('tag.category')
        .getRawMany();

      const categoryCounts = categoryResults.reduce((acc, result) => {
        acc[result.category] = parseInt(result.count);
        return acc;
      }, {});

      return {
        totalTags,
        approvedTags,
        pendingTags,
        rejectedTags,
        categoryCounts,
      };
    } catch (error) {
      this.logger.error(`Failed to get tag stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Increment usage count
  async incrementUsageCount (name: string): Promise<void> {
    try {
      await this.tagRepository.increment({ name }, 'usageCount', 1);
      this.logger.debug(`Incremented usage count for tag: ${name}`);
    } catch (error) {
      this.logger.error(`Failed to increment usage count for tag ${name}: ${error.message}`, error.stack);
      // Don't throw error for usage count updates to avoid breaking main operations
    }
  }

  // Get popular tags
  async getPopularTags (limit: number = 20): Promise<Tag[]> {
    try {
      const tags = await this.tagRepository.find({
        where: { status: TagStatus.Approved },
        order: { usageCount: 'DESC' },
        take: limit,
        relations: ['creator', 'moderator'],
      });

      return tags.map(tag => this.entityToTag(tag));
    } catch (error) {
      this.logger.error(`Failed to get popular tags: ${error.message}`, error.stack);
      throw error;
    }
  }
}
