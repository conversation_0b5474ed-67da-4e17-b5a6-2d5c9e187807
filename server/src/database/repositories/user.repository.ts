import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, ILike } from 'typeorm';
import { UserEntity, AuthTokenEntity, SessionEntity, UserFollowEntity } from '../entities';
import { User, AuthToken, Session } from '@/types/global';

@Injectable()
export class UserRepository {
  private readonly logger = new Logger(UserRepository.name);

  constructor(
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
    @InjectRepository(AuthTokenEntity)
    private authTokenRepository: Repository<AuthTokenEntity>,
    @InjectRepository(SessionEntity)
    private sessionRepository: Repository<SessionEntity>,
    @InjectRepository(UserFollowEntity)
    private userFollowRepository: Repository<UserFollowEntity>,
  ) { }

  // Helper method to convert entity to type
  private entityToUser (entity: UserEntity): User {
    return {
      id: entity.id,
      uname: entity.uname,
      status: entity.status,
      email: entity.email,
      emailVerified: entity.emailVerified,
      profile: entity.profile,
      prefs: entity.prefs,
      activity: entity.activity,
      savedContent: entity.savedContent,
      subscribedCommunities: [], // Will be populated when needed
      providerAccounts: entity.providerAccounts || {},
      lastLoginAt: entity.lastLoginAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToAuthToken (entity: AuthTokenEntity): AuthToken {
    return {
      id: entity.id,
      userId: entity.userId,
      provider: entity.provider,
      providerUserId: entity.providerUserId,
      accessToken: entity.accessToken,
      refreshToken: entity.refreshToken,
      expiresAt: entity.expiresAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToSession (entity: SessionEntity): Session {
    return {
      id: entity.id,
      userId: entity.userId,
      token: entity.token,
      ipAddress: entity.ipAddress,
      userAgent: entity.userAgent,
      expiresAt: entity.expiresAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async createUser (userData: Partial<User>): Promise<User> {
    try {
      const userEntity = this.userRepository.create({
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const savedUser = await this.userRepository.save(userEntity);
      this.logger.log(`User created successfully with ID: ${savedUser.id}`);

      return this.entityToUser(savedUser);
    } catch (error) {
      this.logger.error('Failed to create user:', error);
      throw error;
    }
  }

  async findUserById (id: string): Promise<User | null> {
    try {
      const user = await this.userRepository.findOne({ where: { id } });
      return user ? this.entityToUser(user) : null;
    } catch (error) {
      this.logger.error(`Failed to find user by id ${id}:`, error);
      throw error;
    }
  }

  async findUserByEmail (email: string): Promise<User | null> {
    try {
      const user = await this.userRepository.findOne({ where: { email } });
      return user ? this.entityToUser(user) : null;
    } catch (error) {
      this.logger.error(`Failed to find user by email ${email}:`, error);
      throw error;
    }
  }

  async findUserByUsername (uname: string): Promise<User | null> {
    try {
      const user = await this.userRepository.findOne({ where: { uname } });
      return user ? this.entityToUser(user) : null;
    } catch (error) {
      this.logger.error(`Failed to find user by username ${uname}:`, error);
      throw error;
    }
  }

  async findUserByIdOrUsername (identifier: string): Promise<User | null> {
    try {
      const user = await this.userRepository
        .createQueryBuilder('user')
        .where('user.id = :identifier OR user.uname = :identifier', { identifier })
        .getOne();

      return user ? this.entityToUser(user) : null;
    } catch (error) {
      this.logger.error(`Failed to find user by ID or username ${identifier}:`, error);
      throw error;
    }
  }

  async findUserByProviderAccount (
    provider: string,
    providerUserId: string,
  ): Promise<User | null> {
    try {
      this.logger.log(`Searching for user with ${provider} = ${providerUserId}`);

      // Use JSONB query to find user by provider account
      const user = await this.userRepository
        .createQueryBuilder('user')
        .where(`user.provider_accounts ->> :provider = :providerUserId`, {
          provider,
          providerUserId,
        })
        .getOne();

      if (user) {
        this.logger.log(`Found user with ID: ${user.id}`);
        return this.entityToUser(user);
      }

      this.logger.log('No user found for provider account');
      return null;
    } catch (error) {
      this.logger.error(`Failed to find user by provider ${provider}:`, error);
      throw error;
    }
  }

  async updateUser (id: string, userData: Partial<User>): Promise<User> {
    try {
      // Convert User type to UserEntity type for update
      const updateData: any = {
        ...userData,
        updatedAt: new Date(),
      };

      await this.userRepository.update(id, updateData);

      const updatedUser = await this.userRepository.findOne({ where: { id } });
      if (!updatedUser) {
        throw new Error('User not found after update');
      }

      return this.entityToUser(updatedUser);
    } catch (error) {
      this.logger.error(`Failed to update user ${id}:`, error);
      throw error;
    }
  }

  async deleteUser (id: string): Promise<void> {
    try {
      await this.userRepository.delete(id);
    } catch (error) {
      this.logger.error(`Failed to delete user ${id}:`, error);
      throw error;
    }
  }

  // Auth token methods
  async createAuthToken (tokenData: Partial<AuthToken>): Promise<AuthToken> {
    try {
      const tokenEntity = this.authTokenRepository.create({
        ...tokenData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const savedToken = await this.authTokenRepository.save(tokenEntity);
      this.logger.log(`Auth token created successfully with ID: ${savedToken.id}`);

      return this.entityToAuthToken(savedToken);
    } catch (error) {
      this.logger.error('Failed to create auth token:', error);
      throw error;
    }
  }

  async findAuthTokenByUserAndProvider (
    userId: string,
    provider: string,
  ): Promise<AuthToken | null> {
    try {
      const token = await this.authTokenRepository.findOne({
        where: { userId, provider: provider as any },
      });

      return token ? this.entityToAuthToken(token) : null;
    } catch (error) {
      this.logger.error(
        `Failed to find auth token for user ${userId} and provider ${provider}:`,
        error,
      );
      throw error;
    }
  }

  async updateAuthToken (
    id: string,
    tokenData: Partial<AuthToken>,
  ): Promise<AuthToken> {
    try {
      await this.authTokenRepository.update(id, {
        ...tokenData,
        updatedAt: new Date(),
      });

      const updatedToken = await this.authTokenRepository.findOne({ where: { id } });
      if (!updatedToken) {
        throw new Error('Auth token not found after update');
      }

      return this.entityToAuthToken(updatedToken);
    } catch (error) {
      this.logger.error(`Failed to update auth token ${id}:`, error);
      throw error;
    }
  }

  async deleteAuthToken (id: string): Promise<void> {
    try {
      await this.authTokenRepository.delete(id);
      this.logger.log(`Auth token deleted successfully: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete auth token ${id}:`, error);
      throw error;
    }
  }

  // Session methods
  async createSession (sessionData: Partial<Session>): Promise<Session> {
    try {
      const sessionEntity = this.sessionRepository.create({
        ...sessionData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const savedSession = await this.sessionRepository.save(sessionEntity);
      this.logger.log(`Session created successfully with ID: ${savedSession.id}`);

      return this.entityToSession(savedSession);
    } catch (error) {
      this.logger.error('Failed to create session:', error);
      throw error;
    }
  }

  async findSessionByToken (token: string): Promise<Session | null> {
    try {
      const session = await this.sessionRepository
        .createQueryBuilder('session')
        .where('session.token = :token', { token })
        .andWhere('session.expires_at > :now', { now: new Date() })
        .getOne();

      return session ? this.entityToSession(session) : null;
    } catch (error) {
      this.logger.error(`Failed to find session by token:`, error);
      throw error;
    }
  }

  async deleteSession (id: string): Promise<void> {
    try {
      await this.sessionRepository.delete(id);
    } catch (error) {
      this.logger.error(`Failed to delete session ${id}:`, error);
      throw error;
    }
  }

  async deleteExpiredSessions (): Promise<void> {
    try {
      await this.sessionRepository
        .createQueryBuilder()
        .delete()
        .where('expires_at < :now', { now: new Date() })
        .execute();
    } catch (error) {
      this.logger.error('Failed to delete expired sessions:', error);
      throw error;
    }
  }

  // User search methods
  async searchUsers (query: string, limit: number, offset: number): Promise<User[]> {
    try {
      const users = await this.userRepository
        .createQueryBuilder('user')
        .where('user.uname ILIKE :query', { query: `%${query}%` })
        .orWhere("user.profile ->> 'displayName' ILIKE :query", { query: `%${query}%` })
        .orderBy('user.created_at', 'DESC')
        .limit(limit)
        .offset(offset)
        .getMany();

      return users.map(user => this.entityToUser(user));
    } catch (error) {
      this.logger.error(`Failed to search users with query "${query}":`, error);
      throw error;
    }
  }

  async countSearchResults (query: string): Promise<number> {
    try {
      return await this.userRepository
        .createQueryBuilder('user')
        .where('user.uname ILIKE :query', { query: `%${query}%` })
        .orWhere("user.profile ->> 'displayName' ILIKE :query", { query: `%${query}%` })
        .getCount();
    } catch (error) {
      this.logger.error(`Failed to count search results for query "${query}":`, error);
      throw error;
    }
  }

  // User follow methods
  async createUserFollow (followerId: string, followingId: string): Promise<void> {
    try {
      const followEntity = this.userFollowRepository.create({
        followerId,
        followingId,
        createdAt: new Date(),
      });

      await this.userFollowRepository.save(followEntity);
      this.logger.log(`User follow relationship created: ${followerId} -> ${followingId}`);
    } catch (error) {
      this.logger.error(`Failed to create user follow relationship:`, error);
      throw error;
    }
  }

  async removeUserFollow (followerId: string, followingId: string): Promise<void> {
    try {
      await this.userFollowRepository.delete({
        followerId,
        followingId,
      });
      this.logger.log(`User follow relationship removed: ${followerId} -> ${followingId}`);
    } catch (error) {
      this.logger.error(`Failed to remove user follow relationship:`, error);
      throw error;
    }
  }

  async isUserFollowing (followerId: string, followingId: string): Promise<boolean> {
    try {
      const follow = await this.userFollowRepository.findOne({
        where: { followerId, followingId },
      });
      return !!follow;
    } catch (error) {
      this.logger.error(`Failed to check if user is following:`, error);
      throw error;
    }
  }

  async getUserFollowers (userId: string): Promise<User[]> {
    try {
      const follows = await this.userFollowRepository
        .createQueryBuilder('follow')
        .leftJoinAndSelect('follow.follower', 'follower')
        .where('follow.following_id = :userId', { userId })
        .orderBy('follow.created_at', 'DESC')
        .getMany();

      return follows.map(follow => this.entityToUser(follow.follower));
    } catch (error) {
      this.logger.error(`Failed to get followers for user ${userId}:`, error);
      throw error;
    }
  }

  async getUserFollowing (userId: string): Promise<User[]> {
    try {
      const follows = await this.userFollowRepository
        .createQueryBuilder('follow')
        .leftJoinAndSelect('follow.following', 'following')
        .where('follow.follower_id = :userId', { userId })
        .orderBy('follow.created_at', 'DESC')
        .getMany();

      return follows.map(follow => this.entityToUser(follow.following));
    } catch (error) {
      this.logger.error(`Failed to get following for user ${userId}:`, error);
      throw error;
    }
  }
}
