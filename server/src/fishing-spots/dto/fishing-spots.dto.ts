import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsNumber,
  IsBoolean,
  IsArray,
  IsObject,
  ValidateNested,
  Min,
  Max,
  Length,
  IsUUID,
  IsLatitude,
  IsLongitude,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { FishingSpotType, FishingSpotStatus, FishingSpot, User } from '@/types/global';

// Location DTO
export class LocationDto {
  @IsNumber()
  @IsLatitude()
  latitude: number;

  @IsNumber()
  @IsLongitude()
  longitude: number;

  @IsOptional()
  @IsString()
  @Length(1, 200)
  address?: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  city?: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  state?: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  country?: string;

  @IsOptional()
  @IsString()
  @Length(1, 20)
  zipCode?: string;
}

// Fishing conditions DTO
export class FishingConditionsDto {
  @IsString()
  @IsNotEmpty()
  @Length(1, 100)
  waterType: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  depth?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  structure?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fishSpecies?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  bestTimes?: string[];

  @IsOptional()
  @IsString()
  @Length(1, 200)
  seasonality?: string;
}

// Access information DTO
export class AccessInfoDto {
  @IsBoolean()
  isPublic: boolean;

  @IsOptional()
  @IsBoolean()
  requiresPermission?: boolean;

  @IsOptional()
  @IsString()
  @Length(1, 200)
  fees?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  restrictions?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  amenities?: string[];
}

// Create fishing spot DTO
export class CreateFishingSpotDto {
  @IsString()
  @IsNotEmpty()
  @Length(1, 200)
  name: string;

  @IsString()
  @IsNotEmpty()
  @Length(1, 2000)
  description: string;

  @IsEnum(FishingSpotType)
  spotType: FishingSpotType;

  @ValidateNested()
  @Type(() => LocationDto)
  location: LocationDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => FishingConditionsDto)
  conditions?: FishingConditionsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AccessInfoDto)
  access?: AccessInfoDto;
}

// Update fishing spot DTO
export class UpdateFishingSpotDto {
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @Length(1, 200)
  name?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @Length(1, 2000)
  description?: string;

  @IsOptional()
  @IsEnum(FishingSpotType)
  spotType?: FishingSpotType;

  @IsOptional()
  @ValidateNested()
  @Type(() => LocationDto)
  location?: LocationDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => FishingConditionsDto)
  conditions?: FishingConditionsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AccessInfoDto)
  access?: AccessInfoDto;
}

// Fishing spot search query DTO
export class FishingSpotSearchQueryDto {
  @IsOptional()
  @IsString()
  @Length(1, 100)
  search?: string;

  @IsOptional()
  @IsEnum(FishingSpotStatus)
  status?: FishingSpotStatus;

  @IsOptional()
  @IsEnum(FishingSpotType)
  spotType?: FishingSpotType;

  @IsOptional()
  @IsUUID()
  createdById?: string;

  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  isPublic?: boolean;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

// Nearby spots query DTO
export class NearbyFishingSpotsQueryDto {
  @Transform(({ value }) => parseFloat(value))
  @IsNumber()
  @IsLatitude()
  latitude: number;

  @Transform(({ value }) => parseFloat(value))
  @IsNumber()
  @IsLongitude()
  longitude: number;

  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  @IsNumber()
  @Min(0.1)
  @Max(100)
  radiusKm?: number = 10;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

// Pagination query DTO
export class PaginationQueryDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

// User response DTO (simplified for fishing spot responses)
export class UserResponseDto {
  id: string;
  uname: string;
  profile: {
    displayName?: string;
    bio?: string;
    avatar?: string;
  };
}

// Fishing spot response DTO
export class FishingSpotResponseDto {
  id: string;
  name: string;
  description: string;
  spotType: FishingSpotType;
  status: FishingSpotStatus;
  location: {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    zipCode?: string;
  };
  conditions?: {
    waterType: string;
    depth?: string;
    structure?: string[];
    fishSpecies?: string[];
    bestTimes?: string[];
    seasonality?: string;
  };
  access?: {
    isPublic: boolean;
    requiresPermission?: boolean;
    fees?: string;
    restrictions?: string[];
    amenities?: string[];
  };
  rating: number;
  reviewCount: number;
  createdBy: UserResponseDto;
  createdAt: Date;
  updatedAt: Date;
}

// Fishing spot list response DTO
export class FishingSpotListResponseDto {
  spots: FishingSpotResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Report fishing spot DTO
export class ReportFishingSpotDto {
  @IsString()
  @IsNotEmpty()
  @Length(1, 50)
  reason: string;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  description?: string;
}
