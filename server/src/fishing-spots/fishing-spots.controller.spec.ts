import { Test, TestingModule } from '@nestjs/testing';
import { FishingSpotsController, CreatorFishingSpotsController, MyFishingSpotsController } from './fishing-spots.controller';
import { FishingSpotsService } from './fishing-spots.service';
import { JwtAuthGuard } from '@/auth/guards/jwt.guard';
import { CsrfGuard } from '@/security/guards/csrf.guard';
import { FishingSpotType, FishingSpotStatus, UserStatus } from '@/types/global';

describe('FishingSpotsController', () => {
  let controller: FishingSpotsController;
  let service: jest.Mocked<FishingSpotsService>;

  const mockUser = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'test.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light' as const,
      notificationPreferences: [],
      emailDigest: 'Weekly' as const,
      contentVisibility: 'Public' as const,
      twoFactorEnabled: false,
      sessionTimeout: 'Medium' as const,
    },
    activity: {
      postsToday: 0,
      commentsToday: 0,
      votesToday: 0,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    lastLoginAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockFishingSpotResponse = {
    id: 'spot-1',
    name: 'Test Lake',
    description: 'A great fishing spot',
    spotType: FishingSpotType.Lake,
    status: FishingSpotStatus.Active,
    location: {
      latitude: 40.7128,
      longitude: -74.0060,
      city: 'New York',
      state: 'NY',
    },
    conditions: {
      waterType: 'Freshwater',
      fishSpecies: ['Bass', 'Pike'],
    },
    access: {
      isPublic: true,
      requiresPermission: false,
    },
    rating: 4.5,
    reviewCount: 10,
    createdBy: {
      id: 'user-1',
      uname: 'testuser',
      profile: { displayName: 'Test User' },
    },
    tags: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockService = {
      createFishingSpot: jest.fn(),
      getFishingSpotById: jest.fn(),
      updateFishingSpot: jest.fn(),
      deleteFishingSpot: jest.fn(),
      getFishingSpots: jest.fn(),
      getNearbyFishingSpots: jest.fn(),
      getFishingSpotsByCreator: jest.fn(),
      reportFishingSpot: jest.fn(),
    };

    // Mock guards to avoid dependency injection issues
    const mockJwtAuthGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const mockCsrfGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [FishingSpotsController],
      providers: [
        {
          provide: FishingSpotsService,
          useValue: mockService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(CsrfGuard)
      .useValue(mockCsrfGuard)
      .compile();

    controller = module.get<FishingSpotsController>(FishingSpotsController);
    service = module.get(FishingSpotsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getFishingSpots', () => {
    it('should return fishing spots list', async () => {
      const mockResponse = {
        spots: [mockFishingSpotResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };
      service.getFishingSpots.mockResolvedValue(mockResponse);

      const result = await controller.getFishingSpots(
        { page: 1, limit: 20 },
        { user: mockUser }
      );

      expect(service.getFishingSpots).toHaveBeenCalledWith(
        { page: 1, limit: 20 },
        mockUser.id
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle unauthenticated requests', async () => {
      const mockResponse = {
        spots: [mockFishingSpotResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };
      service.getFishingSpots.mockResolvedValue(mockResponse);

      const result = await controller.getFishingSpots(
        { page: 1, limit: 20 },
        {}
      );

      expect(service.getFishingSpots).toHaveBeenCalledWith(
        { page: 1, limit: 20 },
        undefined
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createFishingSpot', () => {
    it('should create a fishing spot', async () => {
      const createDto = {
        name: 'Test Lake',
        description: 'A great fishing spot',
        spotType: FishingSpotType.Lake,
        location: {
          latitude: 40.7128,
          longitude: -74.0060,
          city: 'New York',
          state: 'NY',
        },
        conditions: {
          waterType: 'Freshwater',
          fishSpecies: ['Bass', 'Pike'],
        },
        access: {
          isPublic: true,
        },
      };

      service.createFishingSpot.mockResolvedValue(mockFishingSpotResponse);

      const result = await controller.createFishingSpot(createDto, { user: mockUser });

      expect(service.createFishingSpot).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(result).toEqual(mockFishingSpotResponse);
    });
  });

  describe('getNearbyFishingSpots', () => {
    it('should return nearby fishing spots', async () => {
      const query = {
        latitude: 40.7128,
        longitude: -74.0060,
        radiusKm: 10,
        limit: 20,
      };

      service.getNearbyFishingSpots.mockResolvedValue([mockFishingSpotResponse]);

      const result = await controller.getNearbyFishingSpots(query);

      expect(service.getNearbyFishingSpots).toHaveBeenCalledWith(query);
      expect(result).toEqual([mockFishingSpotResponse]);
    });
  });

  describe('getFishingSpotById', () => {
    it('should return a fishing spot by ID', async () => {
      service.getFishingSpotById.mockResolvedValue(mockFishingSpotResponse);

      const result = await controller.getFishingSpotById('spot-1', { user: mockUser });

      expect(service.getFishingSpotById).toHaveBeenCalledWith('spot-1', mockUser.id);
      expect(result).toEqual(mockFishingSpotResponse);
    });

    it('should handle unauthenticated requests', async () => {
      service.getFishingSpotById.mockResolvedValue(mockFishingSpotResponse);

      const result = await controller.getFishingSpotById('spot-1', {});

      expect(service.getFishingSpotById).toHaveBeenCalledWith('spot-1', undefined);
      expect(result).toEqual(mockFishingSpotResponse);
    });
  });

  describe('updateFishingSpot', () => {
    it('should update a fishing spot', async () => {
      const updateDto = {
        name: 'Updated Lake Name',
        description: 'Updated description',
      };

      const updatedResponse = { ...mockFishingSpotResponse, ...updateDto };
      service.updateFishingSpot.mockResolvedValue(updatedResponse);

      const result = await controller.updateFishingSpot('spot-1', updateDto, { user: mockUser });

      expect(service.updateFishingSpot).toHaveBeenCalledWith('spot-1', updateDto, mockUser.id);
      expect(result).toEqual(updatedResponse);
    });
  });

  describe('deleteFishingSpot', () => {
    it('should delete a fishing spot', async () => {
      service.deleteFishingSpot.mockResolvedValue(undefined);

      await controller.deleteFishingSpot('spot-1', { user: mockUser });

      expect(service.deleteFishingSpot).toHaveBeenCalledWith('spot-1', mockUser.id);
    });
  });

  describe('reportFishingSpot', () => {
    it('should report a fishing spot', async () => {
      const reportDto = {
        reason: 'Inappropriate content',
        description: 'This spot contains inappropriate content',
      };

      service.reportFishingSpot.mockResolvedValue(undefined);

      await controller.reportFishingSpot('spot-1', reportDto, { user: mockUser });

      expect(service.reportFishingSpot).toHaveBeenCalledWith('spot-1', reportDto, mockUser.id);
    });
  });
});

describe('CreatorFishingSpotsController', () => {
  let controller: CreatorFishingSpotsController;
  let service: jest.Mocked<FishingSpotsService>;

  const mockUser = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'test.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light' as const,
      notificationPreferences: [],
      emailDigest: 'Weekly' as const,
      contentVisibility: 'Public' as const,
      twoFactorEnabled: false,
      sessionTimeout: 'Medium' as const,
    },
    activity: {
      postsToday: 0,
      commentsToday: 0,
      votesToday: 0,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    lastLoginAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockService = {
      getFishingSpotsByCreator: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CreatorFishingSpotsController],
      providers: [
        {
          provide: FishingSpotsService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<CreatorFishingSpotsController>(CreatorFishingSpotsController);
    service = module.get(FishingSpotsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getFishingSpotsByCreator', () => {
    it('should return fishing spots by creator', async () => {
      const mockResponse = {
        spots: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0,
      };
      service.getFishingSpotsByCreator.mockResolvedValue(mockResponse);

      const result = await controller.getFishingSpotsByCreator(
        'creator-1',
        { page: 1, limit: 20 },
        { user: mockUser }
      );

      expect(service.getFishingSpotsByCreator).toHaveBeenCalledWith(
        'creator-1',
        { page: 1, limit: 20 },
        mockUser.id
      );
      expect(result).toEqual(mockResponse);
    });
  });
});

describe('MyFishingSpotsController', () => {
  let controller: MyFishingSpotsController;
  let service: jest.Mocked<FishingSpotsService>;

  const mockUser = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'test.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light' as const,
      notificationPreferences: [],
      emailDigest: 'Weekly' as const,
      contentVisibility: 'Public' as const,
      twoFactorEnabled: false,
      sessionTimeout: 'Medium' as const,
    },
    activity: {
      postsToday: 0,
      commentsToday: 0,
      votesToday: 0,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    lastLoginAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockService = {
      getFishingSpotsByCreator: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [MyFishingSpotsController],
      providers: [
        {
          provide: FishingSpotsService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<MyFishingSpotsController>(MyFishingSpotsController);
    service = module.get(FishingSpotsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getMyFishingSpots', () => {
    it('should return current user fishing spots', async () => {
      const mockResponse = {
        spots: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0,
      };
      service.getFishingSpotsByCreator.mockResolvedValue(mockResponse);

      const result = await controller.getMyFishingSpots(
        { page: 1, limit: 20 },
        { user: mockUser }
      );

      expect(service.getFishingSpotsByCreator).toHaveBeenCalledWith(
        mockUser.id,
        { page: 1, limit: 20 },
        mockUser.id
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
