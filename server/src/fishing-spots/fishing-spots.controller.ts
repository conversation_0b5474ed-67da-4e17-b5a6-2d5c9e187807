import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  ValidationPipe,
  ParseUUIDPipe,
} from '@nestjs/common';
import { FishingSpotsService } from './fishing-spots.service';
import { JwtAuthGuard } from '@/auth/guards/jwt.guard';
import { CsrfGuard } from '@/security/guards/csrf.guard';
import { Public } from '@/common/decorators/public.decorator';
import { SkipCsrf } from '@/security/decorators/skip-csrf.decorator';
import {
  CreateFishingSpotDto,
  UpdateFishingSpotDto,
  FishingSpotSearchQueryDto,
  NearbyFishingSpotsQueryDto,
  PaginationQueryDto,
  FishingSpotResponseDto,
  FishingSpotListResponseDto,
  ReportFishingSpotDto,
} from './dto/fishing-spots.dto';
import { User } from '@/types/global';

@Controller('api/spots')
export class FishingSpotsController {
  constructor(private readonly fishingSpotsService: FishingSpotsService) {}

  // Fishing spot CRUD endpoints
  @Get()
  @Public()
  @SkipCsrf()
  async getFishingSpots(
    @Query(ValidationPipe) query: FishingSpotSearchQueryDto,
    @Request() req: { user?: User },
  ): Promise<FishingSpotListResponseDto> {
    return this.fishingSpotsService.getFishingSpots(query, req.user?.id);
  }

  @Post()
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async createFishingSpot(
    @Body(ValidationPipe) createDto: CreateFishingSpotDto,
    @Request() req: { user: User },
  ): Promise<FishingSpotResponseDto> {
    return this.fishingSpotsService.createFishingSpot(createDto, req.user.id);
  }

  @Get('nearby')
  @Public()
  @SkipCsrf()
  async getNearbyFishingSpots(
    @Query(ValidationPipe) query: NearbyFishingSpotsQueryDto,
  ): Promise<FishingSpotResponseDto[]> {
    return this.fishingSpotsService.getNearbyFishingSpots(query);
  }

  @Get(':id')
  @Public()
  @SkipCsrf()
  async getFishingSpotById(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user?: User },
  ): Promise<FishingSpotResponseDto> {
    return this.fishingSpotsService.getFishingSpotById(id, req.user?.id);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async updateFishingSpot(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateFishingSpotDto,
    @Request() req: { user: User },
  ): Promise<FishingSpotResponseDto> {
    return this.fishingSpotsService.updateFishingSpot(id, updateDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async deleteFishingSpot(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: User },
  ): Promise<void> {
    return this.fishingSpotsService.deleteFishingSpot(id, req.user.id);
  }

  @Post(':id/report')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async reportFishingSpot(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) reportDto: ReportFishingSpotDto,
    @Request() req: { user: User },
  ): Promise<void> {
    return this.fishingSpotsService.reportFishingSpot(id, reportDto, req.user.id);
  }
}

// Creator-specific fishing spots controller
@Controller('api/users/:creatorId/spots')
export class CreatorFishingSpotsController {
  constructor(private readonly fishingSpotsService: FishingSpotsService) {}

  @Get()
  @Public()
  @SkipCsrf()
  async getFishingSpotsByCreator(
    @Param('creatorId', ParseUUIDPipe) creatorId: string,
    @Query(ValidationPipe) query: PaginationQueryDto,
    @Request() req: { user?: User },
  ): Promise<FishingSpotListResponseDto> {
    return this.fishingSpotsService.getFishingSpotsByCreator(
      creatorId,
      query,
      req.user?.id
    );
  }
}

// Current user's fishing spots controller
@Controller('api/users/me/spots')
export class MyFishingSpotsController {
  constructor(private readonly fishingSpotsService: FishingSpotsService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @SkipCsrf()
  async getMyFishingSpots(
    @Query(ValidationPipe) query: PaginationQueryDto,
    @Request() req: { user: User },
  ): Promise<FishingSpotListResponseDto> {
    return this.fishingSpotsService.getFishingSpotsByCreator(
      req.user.id,
      query,
      req.user.id
    );
  }
}
