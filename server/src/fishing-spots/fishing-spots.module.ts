import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FishingSpotsController, CreatorFishingSpotsController, MyFishingSpotsController } from './fishing-spots.controller';
import { FishingSpotsService } from './fishing-spots.service';
import { FishingSpotRepository } from '@/database/repositories/fishing-spot.repository';
import { UserRepository } from '@/database/repositories/user.repository';
import { TagRepository } from '@/database/repositories/tag.repository';
import { TagsService } from '@/tags/tags.service';
import { TagNormalizationService } from '@/services/tag-normalization.service';
import { SecurityModule } from '@/security/security.module';
import {
  FishingSpotEntity,
  UserEntity,
  TagEntity,
} from '@/database/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      FishingSpotEntity,
      UserEntity,
      TagEntity,
    ]),
    SecurityModule,
  ],
  controllers: [
    FishingSpotsController,
    CreatorFishingSpotsController,
    MyFishingSpotsController,
  ],
  providers: [
    FishingSpotsService,
    FishingSpotRepository,
    UserRepository,
    TagRepository,
    TagsService,
    TagNormalizationService,
  ],
  exports: [
    FishingSpotsService,
    FishingSpotRepository,
  ],
})
export class FishingSpotsModule { }
