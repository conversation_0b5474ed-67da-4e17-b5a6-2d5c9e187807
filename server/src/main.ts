import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import cookieParser from 'cookie-parser';
import helmet from 'helmet';
import { SecurityService } from './security/security.service';

async function bootstrap () {
  const app = await NestFactory.create(AppModule);

  // Get security service for configuration
  const securityService = app.get(SecurityService);

  // Enable security headers with Helmet
  app.use(helmet(securityService.getHelmetConfig()));

  // Enable cookie parsing
  app.use(cookieParser());

  // Enable CORS for web frontend
  app.enableCors({
    origin: [
      'http://localhost:3001', // NextJS dev server
      'http://localhost:3000', // Fallback
    ],
    credentials: true, // Important for httpOnly cookies
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-CSRF-Token',
      'X-XSRF-Token',
    ],
  });

  await app.listen(process.env.PORT ?? 3000);
}
void bootstrap();
