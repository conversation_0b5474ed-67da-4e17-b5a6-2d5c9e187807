import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '@/auth/guards/jwt.guard';
import { CsrfGuard } from '@/security/guards/csrf.guard';
import { Public } from '@/common/decorators/public.decorator';
import { SkipCsrf } from '@/security/decorators/skip-csrf.decorator';
import { PostsService } from './posts.service';
import { User } from '@/types/global';
import {
  CreatePostDto,
  UpdatePostDto,
  VotePostDto,
  PostSearchQueryDto,
  PostResponseDto,
  PostListResponseDto,
  PostVoteStatsDto,
  PaginationQueryDto
} from './dto/posts.dto';

@Controller('v1/posts')
export class PostsController {
  constructor(private readonly postsService: PostsService) { }

  // Post CRUD endpoints
  @Get()
  @Public()
  @SkipCsrf()
  async getPosts (
    @Query(ValidationPipe) query: PostSearchQueryDto,
    @Request() req: { user?: User },
  ): Promise<PostListResponseDto> {
    return this.postsService.getPosts(query, req.user?.id);
  }

  @Post()
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async createPost (
    @Body(ValidationPipe) createDto: CreatePostDto,
    @Request() req: { user: User },
  ): Promise<PostResponseDto> {
    return this.postsService.createPost(createDto, req.user.id);
  }

  @Get(':id')
  @Public()
  @SkipCsrf()
  async getPost (
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user?: User },
  ): Promise<PostResponseDto> {
    return this.postsService.getPost(id, req.user?.id);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async updatePost (
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdatePostDto,
    @Request() req: { user: User },
  ): Promise<PostResponseDto> {
    return this.postsService.updatePost(id, updateDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePost (
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: User },
  ): Promise<void> {
    return this.postsService.deletePost(id, req.user.id);
  }

  // Post voting endpoints
  @Post(':id/vote')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async voteOnPost (
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) voteDto: VotePostDto,
    @Request() req: { user: User },
  ): Promise<PostVoteStatsDto> {
    return this.postsService.voteOnPost(id, voteDto, req.user.id);
  }

  @Delete(':id/vote')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async removeVote (
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: User },
  ): Promise<PostVoteStatsDto> {
    return this.postsService.removeVote(id, req.user.id);
  }

  @Get(':id/votes')
  @Public()
  @SkipCsrf()
  async getPostVoteStats (
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user?: User },
  ): Promise<PostVoteStatsDto> {
    return this.postsService.getPostVoteStats(id, req.user?.id);
  }

  // Community-specific post endpoints
  @Get('community/:communityId')
  @Public()
  @SkipCsrf()
  async getPostsByCommunity (
    @Param('communityId', ParseUUIDPipe) communityId: string,
    @Query(ValidationPipe) query: PaginationQueryDto,
    @Request() req: { user?: User },
  ): Promise<PostListResponseDto> {
    return this.postsService.getPostsByCommunity(communityId, query, req.user?.id);
  }

  // Author-specific post endpoints
  @Get('author/:authorId')
  @Public()
  @SkipCsrf()
  async getPostsByAuthor (
    @Param('authorId', ParseUUIDPipe) authorId: string,
    @Query(ValidationPipe) query: PaginationQueryDto,
    @Request() req: { user?: User },
  ): Promise<PostListResponseDto> {
    return this.postsService.getPostsByAuthor(authorId, query, req.user?.id);
  }

}
