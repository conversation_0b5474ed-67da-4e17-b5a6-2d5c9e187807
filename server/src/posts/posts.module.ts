import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PostsController } from './posts.controller';
import { PostsService } from './posts.service';
import { PostRepository, CommunityRepository, UserRepository, TagRepository } from '@/database/repositories';
import { SecurityModule } from '@/security/security.module';
import { ConfigModule } from '@/config/config.module';
import { TagsService } from '@/tags/tags.service';
import { TagNormalizationService } from '@/services/tag-normalization.service';
import {
  PostEntity,
  PostVoteEntity,
  UserEntity,
  CommunityEntity,
  CommunityMemberEntity,
  FishingSpotEntity,
  TagEntity
} from '@/database/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PostEntity,
      PostVoteEntity,
      UserEntity,
      CommunityEntity,
      CommunityMemberEntity,
      FishingSpotEntity,
      TagEntity,
    ]),
    SecurityModule,
    ConfigModule,
  ],
  controllers: [PostsController],
  providers: [
    PostsService,
    PostRepository,
    CommunityRepository,
    UserRepository,
    TagRepository,
    TagsService,
    TagNormalizationService,
  ],
  exports: [PostsService, PostRepository],
})
export class PostsModule { }
