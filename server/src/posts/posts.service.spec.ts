import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PostsService } from './posts.service';
import { PostRepository } from '@/database/repositories/post.repository';
import { CommunityRepository, UserRepository } from '@/database/repositories';
import { TagsService } from '@/tags/tags.service';
import {
  Post,
  User,
  Community,
  PostStatus,
  VoteType,
  MemberRole,
  CommunityVisibility,
  InvitePermission,
  PostModeration,
  UserStatus
} from '@/types/global';

describe('PostsService', () => {
  let service: PostsService;
  let postRepository: jest.Mocked<PostRepository>;
  let communityRepository: jest.Mocked<CommunityRepository>;
  let userRepository: jest.Mocked<UserRepository>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'test.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 5,
      commentsToday: 10,
      votesToday: 15,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCommunity: Community = {
    id: 'community-1',
    name: 'Test Community',
    description: 'A test community for fishing enthusiasts',
    rules: 'Be respectful and follow community guidelines',
    visibility: CommunityVisibility.Public,
    invitePermission: InvitePermission.Moderators,
    postModeration: PostModeration.None,
    commentModeration: PostModeration.None,
    pic: 'community.jpg',
    banner: 'community-banner.jpg',
    owner: mockUser,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPost: Post = {
    id: 'post-1',
    title: 'Test Post',
    status: PostStatus.Published,
    content: 'This is a test post about fishing',
    author: mockUser,
    community: mockCommunity,
    fishingSpots: [],
    comments: [],
    tags: [],
    moderation: undefined,
    upvotes: 5,
    downvotes: 1,
    views: 100,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockPostRepository = {
      createPost: jest.fn(),
      findPostById: jest.fn(),
      updatePost: jest.fn(),
      deletePost: jest.fn(),
      findPosts: jest.fn(),
      findPostsByCommunity: jest.fn(),
      findPostsByAuthor: jest.fn(),
      voteOnPost: jest.fn(),
      removeVote: jest.fn(),
      getUserVote: jest.fn(),
      getPostVoteStats: jest.fn(),
      incrementViews: jest.fn(),
      moderatePost: jest.fn(),
      updatePostStatus: jest.fn(),
      addTagsToPost: jest.fn(),
      removeTagsFromPost: jest.fn(),
      replaceTagsInPost: jest.fn(),
      addFishingSpotsToPost: jest.fn(),
      removeFishingSpotsFromPost: jest.fn(),
      replaceFishingSpotsInPost: jest.fn(),
    };

    const mockCommunityRepository = {
      findCommunityById: jest.fn(),
      isUserMember: jest.fn(),
      getUserMembership: jest.fn(),
    };

    const mockUserRepository = {
      findUserById: jest.fn(),
    };

    const mockTagsService = {
      getOrCreateTags: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostsService,
        {
          provide: PostRepository,
          useValue: mockPostRepository,
        },
        {
          provide: CommunityRepository,
          useValue: mockCommunityRepository,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
        {
          provide: TagsService,
          useValue: mockTagsService,
        },
      ],
    }).compile();

    service = module.get<PostsService>(PostsService);
    postRepository = module.get(PostRepository);
    communityRepository = module.get(CommunityRepository);
    userRepository = module.get(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createPost', () => {
    it('should create a post successfully', async () => {
      const createDto = {
        title: 'New Post',
        content: 'This is a new post',
        communityId: mockCommunity.id,
      };

      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);
      communityRepository.isUserMember.mockResolvedValue(true);
      postRepository.createPost.mockResolvedValue(mockPost);

      const result = await service.createPost(createDto, mockUser.id);

      expect(result).toBeDefined();
      expect(result.title).toBe(mockPost.title);
      expect(postRepository.createPost).toHaveBeenCalledWith({
        title: createDto.title,
        content: createDto.content,
        status: PostStatus.Published,
        authorId: mockUser.id,
        communityId: createDto.communityId,
        fishingSpotId: undefined,
      });
    });

    it('should throw ForbiddenException if user is not a community member', async () => {
      const createDto = {
        title: 'New Post',
        content: 'This is a new post',
        communityId: mockCommunity.id,
      };

      communityRepository.findCommunityById.mockResolvedValue(mockCommunity);
      communityRepository.isUserMember.mockResolvedValue(false);

      await expect(service.createPost(createDto, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });

    it('should throw NotFoundException if community does not exist', async () => {
      const createDto = {
        title: 'New Post',
        content: 'This is a new post',
        communityId: 'non-existent-community',
      };

      communityRepository.findCommunityById.mockResolvedValue(null);

      await expect(service.createPost(createDto, mockUser.id)).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe('getPost', () => {
    it('should return a post successfully', async () => {
      postRepository.findPostById.mockResolvedValue(mockPost);
      postRepository.incrementViews.mockResolvedValue(undefined);

      const result = await service.getPost(mockPost.id, 'other-user-id');

      expect(result).toBeDefined();
      expect(result.id).toBe(mockPost.id);
      expect(postRepository.incrementViews).toHaveBeenCalledWith(mockPost.id);
    });

    it('should not increment views for the author', async () => {
      postRepository.findPostById.mockResolvedValue(mockPost);

      const result = await service.getPost(mockPost.id, mockUser.id);

      expect(result).toBeDefined();
      expect(postRepository.incrementViews).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if post does not exist', async () => {
      postRepository.findPostById.mockResolvedValue(null);

      await expect(service.getPost('non-existent-id')).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException for private community posts', async () => {
      const privatePost = {
        ...mockPost,
        community: { ...mockCommunity, visibility: CommunityVisibility.Private },
      };

      postRepository.findPostById.mockResolvedValue(privatePost);
      communityRepository.isUserMember.mockResolvedValue(false);

      await expect(service.getPost(privatePost.id, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('updatePost', () => {
    it('should update a post successfully by author', async () => {
      const updateDto = {
        title: 'Updated Post',
        content: 'Updated content',
      };

      postRepository.findPostById.mockResolvedValue(mockPost);
      postRepository.updatePost.mockResolvedValue({ ...mockPost, ...updateDto });

      const result = await service.updatePost(mockPost.id, updateDto, mockUser.id);

      expect(result).toBeDefined();
      expect(result.title).toBe(updateDto.title);
      expect(postRepository.updatePost).toHaveBeenCalledWith(mockPost.id, {
        title: updateDto.title,
        content: updateDto.content,
        status: undefined,
        fishingSpotId: undefined,
      });
    });

    it('should throw ForbiddenException if user is not author or moderator', async () => {
      const updateDto = { title: 'Updated Post' };

      postRepository.findPostById.mockResolvedValue(mockPost);
      communityRepository.getUserMembership.mockResolvedValue(null);

      await expect(service.updatePost(mockPost.id, updateDto, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('deletePost', () => {
    it('should delete a post successfully by author', async () => {
      postRepository.findPostById.mockResolvedValue(mockPost);
      postRepository.deletePost.mockResolvedValue(undefined);

      await service.deletePost(mockPost.id, mockUser.id);

      expect(postRepository.deletePost).toHaveBeenCalledWith(mockPost.id);
    });

    it('should throw NotFoundException if post does not exist', async () => {
      postRepository.findPostById.mockResolvedValue(null);

      await expect(service.deletePost('non-existent-id', mockUser.id)).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe('getPosts', () => {
    it('should return posts successfully', async () => {
      const query = { page: 1, limit: 20 };
      const mockResult = { posts: [mockPost], total: 1 };

      postRepository.findPosts.mockResolvedValue(mockResult);

      const result = await service.getPosts(query);

      expect(result).toBeDefined();
      expect(result.posts).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.totalPages).toBe(1);
    });

    it('should filter by community and check permissions for private communities', async () => {
      const query = { page: 1, limit: 20, communityId: mockCommunity.id };
      const privateCommunity = { ...mockCommunity, visibility: CommunityVisibility.Private };

      communityRepository.findCommunityById.mockResolvedValue(privateCommunity);
      communityRepository.isUserMember.mockResolvedValue(false);

      await expect(service.getPosts(query, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('voteOnPost', () => {
    it('should vote on post successfully', async () => {
      const voteDto = { voteType: VoteType.Upvote };
      const mockStats = { upvotes: 6, downvotes: 1 };

      postRepository.findPostById.mockResolvedValue(mockPost);
      postRepository.voteOnPost.mockResolvedValue(undefined);
      postRepository.getPostVoteStats.mockResolvedValue(mockStats);
      postRepository.getUserVote.mockResolvedValue(VoteType.Upvote);

      const result = await service.voteOnPost(mockPost.id, voteDto, 'other-user-id');

      expect(result).toBeDefined();
      expect(result.upvotes).toBe(6);
      expect(result.userVote).toBe(VoteType.Upvote);
      expect(postRepository.voteOnPost).toHaveBeenCalledWith(
        'other-user-id',
        mockPost.id,
        VoteType.Upvote
      );
    });

    it('should throw BadRequestException if user tries to vote on own post', async () => {
      const voteDto = { voteType: VoteType.Upvote };

      postRepository.findPostById.mockResolvedValue(mockPost);

      await expect(service.voteOnPost(mockPost.id, voteDto, mockUser.id)).rejects.toThrow(
        BadRequestException
      );
    });

    it('should throw ForbiddenException for private community posts', async () => {
      const voteDto = { voteType: VoteType.Upvote };
      const privatePost = {
        ...mockPost,
        community: { ...mockCommunity, visibility: CommunityVisibility.Private },
      };

      postRepository.findPostById.mockResolvedValue(privatePost);
      communityRepository.isUserMember.mockResolvedValue(false);

      await expect(service.voteOnPost(privatePost.id, voteDto, 'other-user-id')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('removeVote', () => {
    it('should remove vote successfully', async () => {
      const mockStats = { upvotes: 4, downvotes: 1 };

      postRepository.findPostById.mockResolvedValue(mockPost);
      postRepository.removeVote.mockResolvedValue(undefined);
      postRepository.getPostVoteStats.mockResolvedValue(mockStats);

      const result = await service.removeVote(mockPost.id, 'other-user-id');

      expect(result).toBeDefined();
      expect(result.upvotes).toBe(4);
      expect(postRepository.removeVote).toHaveBeenCalledWith('other-user-id', mockPost.id);
    });
  });

  describe('moderatePost', () => {
    it('should moderate post successfully by community owner', async () => {
      const moderateDto = { status: PostStatus.Flagged, reason: 'Spam content' };
      const moderatedPost = { ...mockPost, status: PostStatus.Flagged };

      postRepository.findPostById.mockResolvedValue(mockPost);
      postRepository.updatePostStatus.mockResolvedValue(moderatedPost);
      postRepository.moderatePost.mockResolvedValue(moderatedPost);

      const result = await service.moderatePost(mockPost.id, moderateDto, mockUser.id);

      expect(result).toBeDefined();
      expect(result.status).toBe(PostStatus.Flagged);
      expect(postRepository.updatePostStatus).toHaveBeenCalledWith(mockPost.id, PostStatus.Flagged);
    });

    it('should allow moderators to moderate posts', async () => {
      const moderateDto = { status: PostStatus.Deleted, reason: 'Inappropriate content' };
      const moderatedPost = { ...mockPost, status: PostStatus.Deleted };
      const mockMembership = {
        id: 'member-1',
        user: mockUser,
        community: mockCommunity,
        role: MemberRole.Moderator,
        joinedAt: new Date(),
        updatedAt: new Date(),
      };

      postRepository.findPostById.mockResolvedValue(mockPost);
      communityRepository.getUserMembership.mockResolvedValue(mockMembership);
      postRepository.updatePostStatus.mockResolvedValue(moderatedPost);
      postRepository.moderatePost.mockResolvedValue(moderatedPost);

      const result = await service.moderatePost(mockPost.id, moderateDto, 'moderator-id');

      expect(result).toBeDefined();
      expect(result.status).toBe(PostStatus.Deleted);
    });

    it('should throw ForbiddenException if user is not a moderator', async () => {
      const moderateDto = { status: PostStatus.Flagged, reason: 'Test' };
      const mockMembership = {
        id: 'member-1',
        user: mockUser,
        community: mockCommunity,
        role: MemberRole.Member,
        joinedAt: new Date(),
        updatedAt: new Date(),
      };

      postRepository.findPostById.mockResolvedValue(mockPost);
      communityRepository.getUserMembership.mockResolvedValue(mockMembership);

      await expect(service.moderatePost(mockPost.id, moderateDto, 'member-id')).rejects.toThrow(
        ForbiddenException
      );
    });
  });
});
