import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  ConflictException
} from '@nestjs/common';
import { PostRepository } from '@/database/repositories/post.repository';
import { CommunityRepository, UserRepository } from '@/database/repositories';
import { TagsService } from '@/tags/tags.service';
import { AppConfigService } from '@/config/config.service';
import {
  Post,
  User,
  Community,
  PostStatus,
  VoteType,
  MemberRole,
  CommunityVisibility
} from '@/types/global';
import {
  CreatePostDto,
  UpdatePostDto,
  VotePostDto,
  PostSearchQueryDto,
  ModeratePostDto,
  PostResponseDto,
  PostListResponseDto,
  PostVoteStatsDto,
  PublicUserProfileDto,
  CommunityBasicDto,
  FishingSpotBasicDto,
  TagDto,
  PaginationQueryDto
} from './dto/posts.dto';

@Injectable()
export class PostsService {
  private readonly logger = new Logger(PostsService.name);

  constructor(
    private readonly postRepository: PostRepository,
    private readonly communityRepository: CommunityRepository,
    private readonly userRepository: UserRepository,
    private readonly tagsService: TagsService,
    private readonly configService: AppConfigService,
  ) { }

  // Helper method to convert User to PublicUserProfileDto
  private userToPublicProfile (user: User): PublicUserProfileDto {
    return {
      id: user.id,
      uname: user.uname,
      profile: user.profile,
      activity: {
        postsCount: user.activity.postsToday, // TODO: Replace with actual counts when available
        commentsCount: user.activity.commentsToday, // TODO: Replace with actual counts when available
        followersCount: 0, // TODO: Implement follower count
        followingCount: 0, // TODO: Implement following count
      },
      createdAt: user.createdAt,
    };
  }

  // Helper method to convert Community to CommunityBasicDto
  private communityToBasic (community: Community): CommunityBasicDto {
    return {
      id: community.id,
      name: community.name,
      description: community.description,
      pic: community.pic,
      banner: community.banner,
    };
  }

  // Helper method to convert Post to PostResponseDto
  private async postToResponse (
    post: Post,
    currentUserId?: string
  ): Promise<PostResponseDto> {
    let userVote: VoteType | undefined;

    // Get user's vote if authenticated
    if (currentUserId) {
      try {
        userVote = await this.postRepository.getUserVote(currentUserId, post.id);
      } catch (error) {
        this.logger.warn(`Failed to get user vote for post ${post.id}: ${error.message}`);
      }
    }

    return {
      id: post.id,
      title: post.title,
      status: post.status,
      content: post.content,
      author: this.userToPublicProfile(post.author),
      community: this.communityToBasic(post.community),
      fishingSpots: post.fishingSpots.map(spot => ({
        id: spot.id,
        name: spot.name,
        description: spot.description,
        coordinates: spot.coordinates,
        typesOfFish: spot.typesOfFish,
      })),
      tags: post.tags.map(tag => ({
        name: tag.name,
        displayName: tag.displayName,
        description: tag.description,
        category: tag.category,
        color: tag.color,
        status: tag.status,
        usageCount: tag.usageCount,
      })),
      upvotes: post.upvotes,
      downvotes: post.downvotes,
      views: post.views,
      userVote,
      createdAt: post.createdAt,
      updatedAt: post.updatedAt,
    };
  }

  // Helper method to check if user can post in community
  private async checkPostPermission (communityId: string, userId: string): Promise<void> {
    const community = await this.communityRepository.findCommunityById(communityId);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    // Check if user is a member of the community
    const isMember = await this.communityRepository.isUserMember(communityId, userId);

    if (!isMember && community.owner.id !== userId) {
      throw new ForbiddenException('You must be a member of this community to post');
    }
  }

  // Helper method to check if user can modify post
  private async checkModifyPermission (post: Post, userId: string): Promise<void> {
    // Get user to check if they're a super user
    const user = await this.userRepository.findUserById(userId);
    if (!user) {
      throw new ForbiddenException('User not found');
    }

    // Super users can modify any post
    if (this.configService.isSuperUser(user.uname)) {
      return;
    }

    // Author can always modify their own posts
    if (post.author.id === userId) {
      return;
    }

    // Community owner can modify posts
    if (post.community.owner.id === userId) {
      return;
    }

    // Check if user is a moderator or admin in the community
    const membership = await this.communityRepository.getUserMembership(post.community.id, userId);

    if (!membership) {
      throw new ForbiddenException('You do not have permission to modify this post');
    }

    const roleHierarchy = {
      [MemberRole.Member]: 0,
      [MemberRole.Moderator]: 1,
      [MemberRole.Admin]: 2,
    };

    if (roleHierarchy[membership.role] < roleHierarchy[MemberRole.Moderator]) {
      throw new ForbiddenException('You do not have permission to modify this post');
    }
  }

  // Post CRUD operations
  async createPost (createDto: CreatePostDto, authorId: string): Promise<PostResponseDto> {
    try {
      // Check if user can post in the community
      await this.checkPostPermission(createDto.communityId, authorId);

      // Validate fishing spots if provided
      if (createDto.fishingSpotIds && createDto.fishingSpotIds.length > 0) {
        // TODO: Add fishing spot validation when needed
      }

      // Create the post
      const post = await this.postRepository.createPost({
        title: createDto.title,
        content: createDto.content,
        status: createDto.status || PostStatus.Published,
        authorId,
        communityId: createDto.communityId,
      });

      let finalPost = post;

      // Add fishing spots if provided
      if (createDto.fishingSpotIds && createDto.fishingSpotIds.length > 0) {
        finalPost = await this.postRepository.addFishingSpotsToPost(post.id, createDto.fishingSpotIds);
      }

      // Add tags if provided
      if (createDto.tagNames && createDto.tagNames.length > 0) {
        const tags = await this.tagsService.getOrCreateTags(createDto.tagNames, authorId);
        const tagNames = tags.map(tag => tag.name);
        finalPost = await this.postRepository.addTagsToPost(finalPost.id, tagNames);
      }

      this.logger.log(`Created post: ${finalPost.title} (${finalPost.id}) by user ${authorId}`);
      return this.postToResponse(finalPost, authorId);
    } catch (error) {
      this.logger.error(`Failed to create post: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getPost (id: string, currentUserId?: string): Promise<PostResponseDto> {
    const post = await this.postRepository.findPostById(id);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Check if user can view the post (for private communities)
    if (post.community.visibility === CommunityVisibility.Private && currentUserId) {
      const isMember = await this.communityRepository.isUserMember(post.community.id, currentUserId);
      if (!isMember && post.community.owner.id !== currentUserId && post.author.id !== currentUserId) {
        throw new ForbiddenException('This post is in a private community');
      }
    }

    // Increment view count (but not for the author)
    if (currentUserId !== post.author.id) {
      await this.postRepository.incrementViews(id);
    }

    return this.postToResponse(post, currentUserId);
  }

  async updatePost (
    id: string,
    updateDto: UpdatePostDto,
    userId: string
  ): Promise<PostResponseDto> {
    const post = await this.postRepository.findPostById(id);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Check permissions
    await this.checkModifyPermission(post, userId);

    // Validate fishing spots if provided
    if (updateDto.fishingSpotIds && updateDto.fishingSpotIds.length > 0) {
      // TODO: Add fishing spot validation when needed
    }

    let updatedPost = await this.postRepository.updatePost(id, {
      title: updateDto.title,
      content: updateDto.content,
      status: updateDto.status,
    });

    // Update fishing spots if provided
    if (updateDto.fishingSpotIds !== undefined) {
      updatedPost = await this.postRepository.replaceFishingSpotsInPost(id, updateDto.fishingSpotIds);
    }

    // Update tags if provided
    if (updateDto.tagNames !== undefined) {
      if (updateDto.tagNames.length > 0) {
        const tags = await this.tagsService.getOrCreateTags(updateDto.tagNames, userId);
        const tagNames = tags.map(tag => tag.name);
        updatedPost = await this.postRepository.replaceTagsInPost(id, tagNames);
      } else {
        // Remove all tags
        updatedPost = await this.postRepository.replaceTagsInPost(id, []);
      }
    }

    this.logger.log(`Updated post: ${id} by user ${userId}`);
    return this.postToResponse(updatedPost, userId);
  }

  async deletePost (id: string, userId: string): Promise<void> {
    const post = await this.postRepository.findPostById(id);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Check permissions
    await this.checkModifyPermission(post, userId);

    await this.postRepository.deletePost(id);
    this.logger.log(`Deleted post: ${id} by user ${userId}`);
  }

  // Post listing and search
  async getPosts (query: PostSearchQueryDto, currentUserId?: string): Promise<PostListResponseDto> {
    const filters: any = {
      communityId: query.communityId,
      authorId: query.authorId,
      status: query.status || PostStatus.Published,
      search: query.search,
      tags: query.tags,
    };

    // If filtering by community, check if user can view it
    if (query.communityId && currentUserId) {
      const community = await this.communityRepository.findCommunityById(query.communityId);
      if (community && community.visibility === CommunityVisibility.Private) {
        const isMember = await this.communityRepository.isUserMember(query.communityId, currentUserId);
        if (!isMember && community.owner.id !== currentUserId) {
          throw new ForbiddenException('You cannot view posts from this private community');
        }
      }
    }

    const { posts, total } = await this.postRepository.findPosts(
      query.page || 1,
      query.limit || 20,
      filters
    );

    const postResponses = await Promise.all(
      posts.map(post => this.postToResponse(post, currentUserId))
    );

    const totalPages = Math.ceil(total / (query.limit || 20));

    return {
      posts: postResponses,
      total,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  async getPostsByCommunity (
    communityId: string,
    query: PaginationQueryDto,
    currentUserId?: string
  ): Promise<PostListResponseDto> {
    // Check if user can view community posts
    const community = await this.communityRepository.findCommunityById(communityId);

    if (!community) {
      throw new NotFoundException('Community not found');
    }

    if (community.visibility === CommunityVisibility.Private && currentUserId) {
      const isMember = await this.communityRepository.isUserMember(communityId, currentUserId);
      if (!isMember && community.owner.id !== currentUserId) {
        throw new ForbiddenException('You cannot view posts from this private community');
      }
    }

    const { posts, total } = await this.postRepository.findPostsByCommunity(
      communityId,
      query.page || 1,
      query.limit || 20
    );

    const postResponses = await Promise.all(
      posts.map(post => this.postToResponse(post, currentUserId))
    );

    const totalPages = Math.ceil(total / (query.limit || 20));

    return {
      posts: postResponses,
      total,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  async getPostsByAuthor (
    authorId: string,
    query: PaginationQueryDto,
    currentUserId?: string
  ): Promise<PostListResponseDto> {
    const { posts, total } = await this.postRepository.findPostsByAuthor(
      authorId,
      query.page || 1,
      query.limit || 20
    );

    // Filter out posts from private communities that the current user can't see
    const visiblePosts = [];
    for (const post of posts) {
      if (post.community.visibility === CommunityVisibility.Private && currentUserId) {
        const isMember = await this.communityRepository.isUserMember(post.community.id, currentUserId);
        if (!isMember && post.community.owner.id !== currentUserId && post.author.id !== currentUserId) {
          continue; // Skip this post
        }
      }
      visiblePosts.push(post);
    }

    const postResponses = await Promise.all(
      visiblePosts.map(post => this.postToResponse(post, currentUserId))
    );

    const totalPages = Math.ceil(visiblePosts.length / (query.limit || 20));

    return {
      posts: postResponses,
      total: visiblePosts.length,
      page: query.page || 1,
      limit: query.limit || 20,
      totalPages,
    };
  }

  // Post voting operations
  async voteOnPost (postId: string, voteDto: VotePostDto, userId: string): Promise<PostVoteStatsDto> {
    const post = await this.postRepository.findPostById(postId);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Check if user can vote (must be community member for private communities)
    if (post.community.visibility === CommunityVisibility.Private) {
      const isMember = await this.communityRepository.isUserMember(post.community.id, userId);
      if (!isMember && post.community.owner.id !== userId) {
        throw new ForbiddenException('You cannot vote on posts in this private community');
      }
    }

    // Users cannot vote on their own posts
    if (post.author.id === userId) {
      throw new BadRequestException('You cannot vote on your own post');
    }

    await this.postRepository.voteOnPost(userId, postId, voteDto.voteType);

    // Get updated vote stats
    const stats = await this.postRepository.getPostVoteStats(postId);
    const userVote = await this.postRepository.getUserVote(userId, postId);

    this.logger.log(`User ${userId} voted ${voteDto.voteType} on post ${postId}`);

    return {
      upvotes: stats.upvotes,
      downvotes: stats.downvotes,
      userVote,
    };
  }

  async removeVote (postId: string, userId: string): Promise<PostVoteStatsDto> {
    const post = await this.postRepository.findPostById(postId);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    await this.postRepository.removeVote(userId, postId);

    // Get updated vote stats
    const stats = await this.postRepository.getPostVoteStats(postId);

    this.logger.log(`User ${userId} removed vote from post ${postId}`);

    return {
      upvotes: stats.upvotes,
      downvotes: stats.downvotes,
    };
  }

  async getPostVoteStats (postId: string, userId?: string): Promise<PostVoteStatsDto> {
    const post = await this.postRepository.findPostById(postId);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    const stats = await this.postRepository.getPostVoteStats(postId);
    let userVote: VoteType | undefined;

    if (userId) {
      userVote = await this.postRepository.getUserVote(userId, postId);
    }

    return {
      upvotes: stats.upvotes,
      downvotes: stats.downvotes,
      userVote,
    };
  }

  // Post moderation operations
  async moderatePost (
    postId: string,
    moderateDto: ModeratePostDto,
    moderatorId: string
  ): Promise<PostResponseDto> {
    const post = await this.postRepository.findPostById(postId);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Get moderator user to check if they're a super user
    const moderator = await this.userRepository.findUserById(moderatorId);
    if (!moderator) {
      throw new ForbiddenException('Moderator not found');
    }

    // Super users can moderate any post
    if (this.configService.isSuperUser(moderator.uname)) {
      // Continue to moderation logic
    } else {
      // Check if user can moderate (must be community moderator or above)
      if (post.community.owner.id !== moderatorId) {
        const membership = await this.communityRepository.getUserMembership(post.community.id, moderatorId);

        if (!membership) {
          throw new ForbiddenException('You are not a member of this community');
        }

        const roleHierarchy = {
          [MemberRole.Member]: 0,
          [MemberRole.Moderator]: 1,
          [MemberRole.Admin]: 2,
        };

        if (roleHierarchy[membership.role] < roleHierarchy[MemberRole.Moderator]) {
          throw new ForbiddenException('You do not have moderation permissions');
        }
      }
    }

    // Create moderation data
    const moderationData = {
      moderatedBy: moderatorId,
      moderatedAt: new Date(),
      action: moderateDto.status,
      logs: [{
        id: `mod-${Date.now()}`,
        message: moderateDto.reason || `Post status changed to ${moderateDto.status}`,
        createdBy: moderatorId,
        createdAt: new Date(),
      }],
    };

    // Update post status and moderation data
    const moderatedPost = await this.postRepository.updatePostStatus(postId, moderateDto.status);
    await this.postRepository.moderatePost(postId, moderationData);

    this.logger.log(`Post ${postId} moderated by ${moderatorId}: ${moderateDto.status}`);
    return this.postToResponse(moderatedPost, moderatorId);
  }

  async updatePostStatus (
    postId: string,
    status: PostStatus,
    userId: string
  ): Promise<PostResponseDto> {
    const post = await this.postRepository.findPostById(postId);

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Check permissions
    await this.checkModifyPermission(post, userId);

    const updatedPost = await this.postRepository.updatePostStatus(postId, status);

    this.logger.log(`Post ${postId} status updated to ${status} by ${userId}`);
    return this.postToResponse(updatedPost, userId);
  }
}
