import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { SecurityService } from '../security.service';
import { SKIP_CSRF_KEY } from '../decorators/skip-csrf.decorator';

@Injectable()
export class CsrfGuard implements CanActivate {
  private readonly logger = new Logger(CsrfGuard.name);

  constructor(
    private reflector: Reflector,
    private securityService: SecurityService,
  ) { }

  canActivate (context: ExecutionContext): boolean {
    // Check if CSRF protection is disabled for this route
    const skipCsrf = this.reflector.getAllAndOverride<boolean>(SKIP_CSRF_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (skipCsrf) {
      return true;
    }

    const request = context.switchToHttp().getRequest<Request>();
    const method = request.method.toLowerCase();

    // Only check CSRF for state-changing methods
    if (!['post', 'put', 'patch', 'delete'].includes(method)) {
      return true;
    }

    // Skip CSRF for OAuth callbacks and other safe routes
    const path = request.path;
    if (this.isSafePath(path)) {
      return true;
    }

    // Get CSRF secret from session/cookie
    const csrfSecret = request.cookies?.['csrf-secret'];
    if (!csrfSecret) {
      this.logger.warn(`CSRF secret missing for ${method.toUpperCase()} ${path}`);
      throw new ForbiddenException('CSRF secret missing');
    }

    // Get CSRF token from header or body
    const csrfToken =
      request.headers['x-csrf-token'] as string ||
      request.headers['x-xsrf-token'] as string ||
      request.body?.['_csrf'] as string;

    if (!csrfToken) {
      this.logger.warn(`CSRF token missing for ${method.toUpperCase()} ${path}`);
      throw new ForbiddenException('CSRF token missing');
    }

    // Verify the token
    const isValid = this.securityService.verifyCsrfToken(csrfSecret, csrfToken);
    if (!isValid) {
      this.logger.warn(`Invalid CSRF token for ${method.toUpperCase()} ${path}`);
      throw new ForbiddenException('Invalid CSRF token');
    }

    return true;
  }

  /**
   * Check if the path should skip CSRF protection
   */
  private isSafePath (path: string): boolean {
    const safePaths = [
      '/v1/auth/github/callback',
      '/v1/auth/google/callback',
      '/v1/auth/facebook/callback',
      '/v1/auth/apple/callback',
      '/v1/health',
      '/v1/init-db',
    ];

    return safePaths.some(safePath => path.startsWith(safePath));
  }
}
