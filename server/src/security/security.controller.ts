import {
  Controller,
  Get,
  Req,
  Res,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { Public } from '../common/decorators/public.decorator';
import { SkipCsrf } from './decorators/skip-csrf.decorator';
import { SecurityService } from './security.service';
import { AppConfigService } from '../config/config.service';

@Controller('v1/security')
export class SecurityController {
  constructor(
    private securityService: SecurityService,
    private configService: AppConfigService,
  ) {}

  @Get('csrf-token')
  @Public()
  @SkipCsrf()
  getCsrfToken(@Req() req: Request, @Res() res: Response) {
    // Get or create CSRF secret
    let csrfSecret = req.cookies?.['csrf-secret'];
    
    if (!csrfSecret) {
      csrfSecret = this.securityService.generateCsrfSecret();
      
      // Set the secret in an httpOnly cookie
      res.cookie('csrf-secret', csrfSecret, {
        httpOnly: true,
        secure: this.configService.isProduction,
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        path: '/',
      });
    }

    // Generate a token from the secret
    const csrfToken = this.securityService.generateCsrfToken(csrfSecret);

    return res.status(HttpStatus.OK).json({
      csrfToken,
      message: 'CSRF token generated successfully',
    });
  }
}
