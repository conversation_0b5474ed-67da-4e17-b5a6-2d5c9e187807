import { Module } from '@nestjs/common';
import { SecurityService } from './security.service';
import { SecurityController } from './security.controller';
import { CsrfGuard } from './guards/csrf.guard';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [ConfigModule],
  controllers: [SecurityController],
  providers: [SecurityService, CsrfGuard],
  exports: [SecurityService, CsrfGuard],
})
export class SecurityModule { }
