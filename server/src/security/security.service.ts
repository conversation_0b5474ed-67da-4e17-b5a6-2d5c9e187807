import { Injectable } from '@nestjs/common';
import { AppConfigService } from '../config/config.service';
import Tokens = require('csrf');

@Injectable()
export class SecurityService {
  private csrfTokens: Tokens;

  constructor(private configService: AppConfigService) {
    this.csrfTokens = new Tokens();
  }

  /**
   * Generate a CSRF secret for a session
   */
  generateCsrfSecret (): string {
    return this.csrfTokens.secretSync();
  }

  /**
   * Generate a CSRF token from a secret
   */
  generateCsrfToken (secret: string): string {
    return this.csrfTokens.create(secret);
  }

  /**
   * Verify a CSRF token against a secret
   */
  verifyCsrfToken (secret: string, token: string): boolean {
    return this.csrfTokens.verify(secret, token);
  }

  /**
   * Get Helmet configuration for security headers
   */
  getHelmetConfig () {
    return {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'", this.configService.appUrl],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          baseUri: ["'self'"],
          formAction: ["'self'"],
        },
      },
      crossOriginEmbedderPolicy: false, // Disable for OAuth redirects
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
      noSniff: true,
      frameguard: { action: 'deny' as const },
      xssFilter: true,
      referrerPolicy: { policy: 'strict-origin-when-cross-origin' as const },
    };
  }
}
