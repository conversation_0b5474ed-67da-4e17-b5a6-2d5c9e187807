import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { TagNormalizationService } from './tag-normalization.service';

describe('TagNormalizationService', () => {
  let service: TagNormalizationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TagNormalizationService],
    }).compile();

    service = module.get<TagNormalizationService>(TagNormalizationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('normalizeTagName', () => {
    it('should normalize basic tag names correctly', () => {
      expect(service.normalizeTagName('Largemouth Bass')).toBe('largemouth-bass');
      expect(service.normalizeTagName('Fly Fishing')).toBe('fly-fishing');
      expect(service.normalizeTagName('TROUT')).toBe('trout');
      expect(service.normalizeTagName('catch and release')).toBe('catch-and-release');
    });

    it('should handle parentheses correctly', () => {
      expect(service.normalizeTagName('Rainbow Trout (Steelhead)')).toBe('rainbow-trout-(steelhead)');
      expect(service.normalizeTagName('Bass (Largemouth)')).toBe('bass-(largemouth)');
    });

    it('should remove invalid characters', () => {
      expect(service.normalizeTagName('Bass & Pike')).toBe('bass-pike');
      expect(service.normalizeTagName('Fly-Fishing!')).toBe('fly-fishing');
      expect(service.normalizeTagName('Catch@Release')).toBe('catchrelease');
    });

    it('should handle multiple spaces and hyphens', () => {
      expect(service.normalizeTagName('  Multiple   Spaces  ')).toBe('multiple-spaces');
      expect(service.normalizeTagName('Already--Hyphenated')).toBe('already-hyphenated');
      expect(service.normalizeTagName('-Leading-Trailing-')).toBe('leading-trailing');
    });

    it('should throw error for empty or invalid input', () => {
      expect(() => service.normalizeTagName('')).toThrow(BadRequestException);
      expect(() => service.normalizeTagName('   ')).toThrow(BadRequestException);
      expect(() => service.normalizeTagName(null as any)).toThrow(BadRequestException);
      expect(() => service.normalizeTagName(undefined as any)).toThrow(BadRequestException);
    });

    it('should throw error for too short tags after normalization', () => {
      expect(() => service.normalizeTagName('a')).toThrow(BadRequestException);
      expect(() => service.normalizeTagName('!@#')).toThrow(BadRequestException);
    });

    it('should throw error for too long tags', () => {
      const longTag = 'a'.repeat(51);
      expect(() => service.normalizeTagName(longTag)).toThrow(BadRequestException);
    });

    it('should throw error for reserved tag names', () => {
      expect(() => service.normalizeTagName('admin')).toThrow(BadRequestException);
      expect(() => service.normalizeTagName('SYSTEM')).toThrow(BadRequestException);
      expect(() => service.normalizeTagName('deleted')).toThrow(BadRequestException);
    });
  });

  describe('normalizeTagNames', () => {
    it('should normalize multiple tags and remove duplicates', () => {
      const input = ['Largemouth Bass', 'Fly Fishing', 'LARGEMOUTH BASS', 'fly-fishing'];
      const result = service.normalizeTagNames(input);
      expect(result).toEqual(['largemouth-bass', 'fly-fishing']);
    });

    it('should handle empty array', () => {
      expect(service.normalizeTagNames([])).toEqual([]);
    });

    it('should throw error for non-array input', () => {
      expect(() => service.normalizeTagNames('not an array' as any)).toThrow(BadRequestException);
    });
  });

  describe('createDisplayName', () => {
    it('should create proper display names', () => {
      expect(service.createDisplayName('largemouth-bass')).toBe('Largemouth Bass');
      expect(service.createDisplayName('fly-fishing')).toBe('Fly Fishing');
      expect(service.createDisplayName('catch-and-release')).toBe('Catch And Release');
      expect(service.createDisplayName('trout')).toBe('Trout');
    });

    it('should handle parentheses in display names', () => {
      expect(service.createDisplayName('rainbow-trout-(steelhead)')).toBe('Rainbow Trout (steelhead)');
    });
  });

  describe('isValidNormalizedTag', () => {
    it('should validate properly normalized tags', () => {
      expect(service.isValidNormalizedTag('largemouth-bass')).toBe(true);
      expect(service.isValidNormalizedTag('fly-fishing')).toBe(true);
      expect(service.isValidNormalizedTag('trout')).toBe(true);
    });

    it('should reject improperly normalized tags', () => {
      expect(service.isValidNormalizedTag('Largemouth Bass')).toBe(false);
      expect(service.isValidNormalizedTag('fly_fishing')).toBe(false);
      expect(service.isValidNormalizedTag('TROUT')).toBe(false);
      expect(service.isValidNormalizedTag('')).toBe(false);
    });
  });

  describe('extractTagsFromText', () => {
    it('should extract hashtags from text', () => {
      const text = 'Great day fishing! #LargemouthBass #FlyFishing #CatchAndRelease';
      const result = service.extractTagsFromText(text);
      expect(result).toContain('largemouthbass');
      expect(result).toContain('flyfishing');
      expect(result).toContain('catchandrelease');
    });

    it('should handle text without hashtags', () => {
      const text = 'Just a regular fishing story without hashtags.';
      const result = service.extractTagsFromText(text);
      expect(result).toEqual([]);
    });

    it('should limit number of extracted tags', () => {
      const text = '#tag1 #tag2 #tag3 #tag4 #tag5 #tag6 #tag7 #tag8 #tag9 #tag10 #tag11';
      const result = service.extractTagsFromText(text, 5);
      expect(result.length).toBeLessThanOrEqual(5);
    });
  });

  describe('suggestSimilarTags', () => {
    it('should suggest similar tags', () => {
      const existingTags = ['largemouth-bass', 'smallmouth-bass', 'fly-fishing', 'bait-fishing'];
      const suggestions = service.suggestSimilarTags('bass', existingTags);
      expect(suggestions).toContain('largemouth-bass');
      expect(suggestions).toContain('smallmouth-bass');
    });

    it('should handle typos with Levenshtein distance', () => {
      const existingTags = ['trout', 'bass', 'pike'];
      const suggestions = service.suggestSimilarTags('trou', existingTags);
      expect(suggestions).toContain('trout');
    });

    it('should limit suggestions', () => {
      const existingTags = Array.from({ length: 20 }, (_, i) => `tag-${i}`);
      const suggestions = service.suggestSimilarTags('tag', existingTags);
      expect(suggestions.length).toBeLessThanOrEqual(5);
    });
  });
});
