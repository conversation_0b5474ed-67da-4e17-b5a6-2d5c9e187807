import { Injectable, Logger } from '@nestjs/common';
import { UserRepository } from '../database/repositories/user.repository';

@Injectable()
export class UsernameGeneratorService {
  private readonly logger = new Logger(UsernameGeneratorService.name);

  // Option 1: <adjective>.<noun>
  private readonly OPTION1_ADJECTIVES = [
    'happy',
    'excited',
    'patient',
    'skilled',
    'lucky',
    'clever',
    'bold',
    'calm',
    'eager',
    'focused',
    'determined',
    'adventurous',
    'experienced',
    'passionate',
    'dedicated',
    'enthusiastic',
    'persistent',
    'confident',
    'relaxed',
    'strategic',
  ];

  private readonly OPTION1_NOUNS = [
    'angler',
    'fisher',
    'caster',
    'hooker',
    'trawler',
    'troller',
  ];

  // Option 2: <adjective>4<noun>
  private readonly OPTION2_ADJECTIVES = [
    'fishing',
    'trolling',
    'jigging',
    'spinning',
    'fly-fishing',
    'bobber-fishing',
    'casting',
    'spearfishing',
    'icefishing',
    'bowfishing',
    'pitching',
    'flipping',
  ];

  private readonly OPTION2_NOUNS = [
    'bass',
    'trout',
    'pike',
    'walleye',
    'catfish',
    'salmon',
    'perch',
    'bluegill',
    'largemouth',
    'smallmouth',
  ];

  constructor(private readonly userRepository: UserRepository) { }

  /**
   * Generates a unique username using one of two formats:
   * Option 1: <adjective>.<noun>.<4digits>
   * Option 2: <adjective>4<noun>.<4digits>
   *
   * Randomly chooses option 1 or 2, generates the base username,
   * then tries different 4-digit numbers (1-9999, zero-padded).
   * If 10 attempts fail, falls back to timestamp.
   */
  async generateUsername (): Promise<string> {
    const maxAttempts = 10;

    // Randomly choose between Option 1 and Option 2
    const useOption1 = Math.random() < 0.5;

    // Generate the base username (without numbers)
    const baseUsername = useOption1
      ? this.generateOption1BaseUsername()
      : this.generateOption2BaseUsername();

    this.logger.debug(`Generated base username: ${baseUsername} (using option ${useOption1 ? '1' : '2'})`);

    // Try different 4-digit numbers with the same base
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        // Generate a random 4-digit number (1-9999, zero-padded)
        const randomNumber = Math.floor(Math.random() * 9999) + 1;
        const fourDigitNumber = randomNumber.toString().padStart(4, '0');
        const username = `${baseUsername}.${fourDigitNumber}`;

        // Check if username is available
        const isAvailable = await this.checkUsernameAvailability(username);
        if (isAvailable) {
          this.logger.log(`Generated unique username: ${username}`);
          return username;
        }

        this.logger.debug(
          `Username ${username} is taken, trying again (attempt ${attempt + 1})`,
        );
      } catch (error) {
        this.logger.error(
          `Error generating username on attempt ${attempt + 1}:`,
          error,
        );
      }
    }

    // Fallback: use timestamp-based username
    const fallbackUsername = this.generateFallbackUsername();
    this.logger.warn(
      `Using fallback username after ${maxAttempts} attempts: ${fallbackUsername}`,
    );
    return fallbackUsername;
  }

  /**
   * Generates Option 1 base format: <adjective>.<noun> (without numbers)
   */
  private generateOption1BaseUsername (): string {
    const adjective = this.getRandomElement(this.OPTION1_ADJECTIVES);
    const noun = this.getRandomElement(this.OPTION1_NOUNS);

    return `${adjective}.${noun}`;
  }

  /**
   * Generates Option 2 base format: <adjective>4<noun> (without numbers)
   */
  private generateOption2BaseUsername (): string {
    const adjective = this.getRandomElement(this.OPTION2_ADJECTIVES);
    const noun = this.getRandomElement(this.OPTION2_NOUNS);

    return `${adjective}4${noun}`;
  }

  /**
   * Generates a fallback username when all attempts fail
   */
  private generateFallbackUsername (): string {
    const timestamp = Date.now().toString();
    return `angler.${timestamp}`;
  }

  /**
   * Checks if a username is available (not taken)
   */
  private async checkUsernameAvailability (username: string): Promise<boolean> {
    try {
      const existingUser = await this.userRepository.findUserByUsername(username);
      return existingUser === null;
    } catch (error) {
      this.logger.error(`Error checking username availability for ${username}:`, error);
      return false; // Assume not available on error to be safe
    }
  }

  /**
   * Gets a random element from an array
   */
  private getRandomElement<T> (array: T[]): T {
    const randomIndex = Math.floor(Math.random() * array.length);
    return array[randomIndex];
  }
}
