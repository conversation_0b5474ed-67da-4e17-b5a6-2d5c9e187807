import {
  IsString,
  <PERSON>O<PERSON>al,
  IsEnum,
  IsArray,
  IsInt,
  Min,
  <PERSON>,
  Length,
  IsHexColor,
  ArrayMaxSize,
  ArrayMinSize,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { TagStatus } from '@/types/global';

// Request DTOs
export class CreateTagDto {
  @IsString()
  @Length(2, 50)
  @Transform(({ value }) => value?.trim())
  name: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  @Transform(({ value }) => value?.trim())
  description?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  @Transform(({ value }) => value?.trim())
  category?: string;

  @IsOptional()
  @IsHexColor()
  color?: string;
}

export class CreateMultipleTagsDto {
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(20)
  @IsString({ each: true })
  @Length(2, 50, { each: true })
  @Transform(({ value }) => value?.map((tag: string) => tag?.trim()).filter(Boolean))
  names: string[];

  @IsOptional()
  @IsString()
  @Length(1, 50)
  @Transform(({ value }) => value?.trim())
  category?: string;
}

export class UpdateTagDto {
  @IsOptional()
  @IsString()
  @Length(1, 100)
  @Transform(({ value }) => value?.trim())
  description?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  @Transform(({ value }) => value?.trim())
  category?: string;

  @IsOptional()
  @IsHexColor()
  color?: string;
}

export class ModerateTagDto {
  @IsEnum(TagStatus)
  status: TagStatus;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  @Transform(({ value }) => value?.trim())
  moderationNote?: string;
}

export class TagSearchQueryDto {
  @IsOptional()
  @IsString()
  @Length(1, 100)
  @Transform(({ value }) => value?.trim())
  search?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  @Transform(({ value }) => value?.trim())
  category?: string;

  @IsOptional()
  @IsEnum(TagStatus)
  status?: TagStatus;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  offset?: number = 0;

  @IsOptional()
  @IsString()
  sortBy?: 'name' | 'usageCount' | 'createdAt' | 'updatedAt' = 'usageCount';

  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

export class TagSuggestionsQueryDto {
  @IsString()
  @Length(1, 50)
  @Transform(({ value }) => value?.trim())
  query: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(10)
  limit?: number = 5;
}

// Response DTOs
export class TagResponseDto {
  name: string;
  displayName: string;
  description?: string;
  category?: string;
  color: string;
  status: TagStatus;
  usageCount: number;
  createdBy: {
    id: string;
    username: string;
    displayName: string;
  };
  moderatedBy?: {
    id: string;
    username: string;
    displayName: string;
  };
  createdAt: Date;
  updatedAt: Date;
  moderatedAt?: Date;
}

export class TagListResponseDto {
  tags: TagResponseDto[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

export class TagStatsResponseDto {
  totalTags: number;
  approvedTags: number;
  pendingTags: number;
  rejectedTags: number;
  categoryCounts: Record<string, number>;
  recentTags: TagResponseDto[];
}

export class TagModerationResponseDto {
  tag: TagResponseDto;
  message: string;
}

export class TagSuggestionsResponseDto {
  suggestions: string[];
  query: string;
}

export class BulkTagOperationDto {
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @IsString({ each: true })
  tagNames: string[];

  @IsEnum(TagStatus)
  status: TagStatus;

  @IsOptional()
  @IsString()
  @Length(1, 500)
  @Transform(({ value }) => value?.trim())
  moderationNote?: string;
}

export class BulkTagOperationResponseDto {
  processed: number;
  successful: number;
  failed: number;
  errors: Array<{
    tagName: string;
    error: string;
  }>;
  message: string;
}
