import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ValidationPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { JwtAuthGuard } from '@/auth/guards/jwt.guard';
import { CsrfGuard } from '@/security/guards/csrf.guard';
import { Public } from '@/common/decorators/public.decorator';
import { SkipCsrf } from '@/security/decorators/skip-csrf.decorator';
import { User } from '@/types/global';
import { TagsService } from './tags.service';
import {
  CreateTagDto,
  CreateMultipleTagsDto,
  UpdateTagDto,
  ModerateTagDto,
  TagSearchQueryDto,
  TagSuggestionsQueryDto,
  TagResponseDto,
  TagListResponseDto,
  TagStatsResponseDto,
  TagSuggestionsResponseDto,
  BulkTagOperationDto,
  BulkTagOperationResponseDto,
} from './dto/tags.dto';

@Controller('v1/tags')
export class TagsController {
  constructor(private readonly tagsService: TagsService) {}

  // Public endpoints for browsing tags
  @Get()
  @Public()
  @SkipCsrf()
  async getTags(
    @Query(ValidationPipe) query: TagSearchQueryDto,
  ): Promise<TagListResponseDto> {
    return this.tagsService.searchTags(query);
  }

  @Get('stats')
  @Public()
  @SkipCsrf()
  async getTagStats(): Promise<TagStatsResponseDto> {
    return this.tagsService.getTagStats();
  }

  @Get('suggestions')
  @Public()
  @SkipCsrf()
  async getTagSuggestions(
    @Query(ValidationPipe) query: TagSuggestionsQueryDto,
  ): Promise<TagSuggestionsResponseDto> {
    return this.tagsService.getTagSuggestions(query.query, query.limit);
  }

  @Get(':name')
  @Public()
  @SkipCsrf()
  async getTag(@Param('name') name: string): Promise<TagResponseDto> {
    return this.tagsService.getTag(name);
  }

  // Authenticated endpoints for tag management
  @Post()
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async createTag(
    @Body(ValidationPipe) createDto: CreateTagDto,
    @Request() req: { user: User },
  ): Promise<TagResponseDto> {
    return this.tagsService.createTag(createDto, req.user.id);
  }

  @Post('bulk')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async createMultipleTags(
    @Body(ValidationPipe) createDto: CreateMultipleTagsDto,
    @Request() req: { user: User },
  ): Promise<TagResponseDto[]> {
    return this.tagsService.createMultipleTags(createDto, req.user.id);
  }

  @Put(':name')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async updateTag(
    @Param('name') name: string,
    @Body(ValidationPipe) updateDto: UpdateTagDto,
    @Request() req: { user: User },
  ): Promise<TagResponseDto> {
    return this.tagsService.updateTag(name, updateDto, req.user.id);
  }

  // Moderation endpoints (require moderator permissions)
  @Post(':name/moderate')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async moderateTag(
    @Param('name') name: string,
    @Body(ValidationPipe) moderateDto: ModerateTagDto,
    @Request() req: { user: User },
  ): Promise<TagResponseDto> {
    // TODO: Add role-based authorization for moderators
    return this.tagsService.moderateTag(name, moderateDto, req.user.id);
  }

  @Post('moderate/bulk')
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async bulkModerate(
    @Body(ValidationPipe) bulkDto: BulkTagOperationDto,
    @Request() req: { user: User },
  ): Promise<BulkTagOperationResponseDto> {
    // TODO: Add role-based authorization for moderators
    return this.tagsService.bulkModerate(bulkDto, req.user.id);
  }
}
