import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TagEntity } from '@/database/entities/tag.entity';
import { TagRepository } from '@/database/repositories/tag.repository';
import { TagNormalizationService } from '@/services/tag-normalization.service';
import { SecurityModule } from '@/security/security.module';
import { ConfigModule } from '@/config/config.module';
import { UserRepository } from '@/database/repositories';
import { TagsController } from './tags.controller';
import { TagsService } from './tags.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([TagEntity]),
    SecurityModule,
    ConfigModule,
  ],
  controllers: [TagsController],
  providers: [
    TagsService,
    TagRepository,
    UserRepository,
    TagNormalizationService,
  ],
  exports: [
    TagsService,
    TagRepository,
    TagNormalizationService,
  ],
})
export class TagsModule { }
