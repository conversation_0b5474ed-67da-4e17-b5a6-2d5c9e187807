"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthProvider = exports.FishingSpotType = exports.VoteType = exports.MemberRole = exports.PostModeration = exports.InvitePermission = exports.CommunityVisibility = exports.FishingSpotStatus = exports.CommentStatus = exports.PostStatus = exports.UserStatus = void 0;
var UserStatus;
(function (UserStatus) {
    UserStatus["Active"] = "Active";
    UserStatus["Inactive"] = "Inactive";
    UserStatus["Banned"] = "Banned";
    UserStatus["Deleted"] = "Deleted";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
var PostStatus;
(function (PostStatus) {
    PostStatus["Draft"] = "Draft";
    PostStatus["Published"] = "Published";
    PostStatus["Archived"] = "Archived";
    PostStatus["Deleted"] = "Deleted";
    PostStatus["Flagged"] = "Flagged";
    PostStatus["UnderReview"] = "UnderReview";
})(PostStatus || (exports.PostStatus = PostStatus = {}));
var CommentStatus;
(function (CommentStatus) {
    CommentStatus["Published"] = "Published";
    CommentStatus["Deleted"] = "Deleted";
    CommentStatus["Flagged"] = "Flagged";
    CommentStatus["UnderReview"] = "UnderReview";
})(CommentStatus || (exports.CommentStatus = CommentStatus = {}));
var FishingSpotStatus;
(function (FishingSpotStatus) {
    FishingSpotStatus["Active"] = "Active";
    FishingSpotStatus["Inactive"] = "Inactive";
    FishingSpotStatus["Private"] = "Private";
    FishingSpotStatus["Closed"] = "Closed";
})(FishingSpotStatus || (exports.FishingSpotStatus = FishingSpotStatus = {}));
var CommunityVisibility;
(function (CommunityVisibility) {
    CommunityVisibility["Public"] = "Public";
    CommunityVisibility["Private"] = "Private";
    CommunityVisibility["Restricted"] = "Restricted";
})(CommunityVisibility || (exports.CommunityVisibility = CommunityVisibility = {}));
var InvitePermission;
(function (InvitePermission) {
    InvitePermission["Anyone"] = "Anyone";
    InvitePermission["Members"] = "Members";
    InvitePermission["Moderators"] = "Moderators";
    InvitePermission["Admins"] = "Admins";
})(InvitePermission || (exports.InvitePermission = InvitePermission = {}));
var PostModeration;
(function (PostModeration) {
    PostModeration["None"] = "None";
    PostModeration["PreModeration"] = "PreModeration";
    PostModeration["PostModeration"] = "PostModeration";
})(PostModeration || (exports.PostModeration = PostModeration = {}));
var MemberRole;
(function (MemberRole) {
    MemberRole["Member"] = "Member";
    MemberRole["Moderator"] = "Moderator";
    MemberRole["Admin"] = "Admin";
})(MemberRole || (exports.MemberRole = MemberRole = {}));
var VoteType;
(function (VoteType) {
    VoteType["Upvote"] = "Upvote";
    VoteType["Downvote"] = "Downvote";
})(VoteType || (exports.VoteType = VoteType = {}));
var FishingSpotType;
(function (FishingSpotType) {
    FishingSpotType["Lake"] = "Lake";
    FishingSpotType["River"] = "River";
    FishingSpotType["Ocean"] = "Ocean";
    FishingSpotType["Pond"] = "Pond";
    FishingSpotType["Stream"] = "Stream";
    FishingSpotType["Bay"] = "Bay";
    FishingSpotType["Reservoir"] = "Reservoir";
    FishingSpotType["Other"] = "Other";
})(FishingSpotType || (exports.FishingSpotType = FishingSpotType = {}));
var AuthProvider;
(function (AuthProvider) {
    AuthProvider["Google"] = "google";
    AuthProvider["Facebook"] = "facebook";
    AuthProvider["GitHub"] = "github";
    AuthProvider["Apple"] = "apple";
})(AuthProvider || (exports.AuthProvider = AuthProvider = {}));
//# sourceMappingURL=global.js.map