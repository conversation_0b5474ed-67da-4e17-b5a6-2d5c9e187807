import {
  IsString,
  IsEmail,
  IsOptional,
  IsUrl,
  IsBoolean,
  IsEnum,
  IsNumber,
  Min,
  Max,
  MinLength,
  MaxLength,
  IsArray,
  ValidateNested,
  IsDateString,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

// Social links DTO (defined first to avoid circular references)
export class SocialLinksDto {
  @IsOptional()
  @IsUrl()
  @MaxLength(200)
  twitter?: string;

  @IsOptional()
  @IsUrl()
  @MaxLength(200)
  instagram?: string;

  @IsOptional()
  @IsUrl()
  @MaxLength(200)
  youtube?: string;

  @IsOptional()
  @IsUrl()
  @MaxLength(200)
  tiktok?: string;
}

// User profile update DTO (defined before main update DTO)
export class UserProfileUpdateDto {
  @IsOptional()
  @IsString()
  @MaxLength(100)
  displayName?: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  bio?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  location?: string;

  @IsOptional()
  @IsUrl()
  @MaxLength(200)
  website?: string;

  @IsOptional()
  @IsUrl()
  @MaxLength(500)
  pic?: string;

  @IsOptional()
  @IsUrl()
  @MaxLength(500)
  banner?: string;

  @IsOptional()
  @IsBoolean()
  isGuide?: boolean;

  @IsOptional()
  @IsBoolean()
  isVendor?: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => SocialLinksDto)
  socialLinks?: SocialLinksDto;
}

// User preferences update DTO
export class UserPreferencesUpdateDto {
  @IsOptional()
  @IsEnum(['Light', 'Dark', 'System'])
  theme?: 'Light' | 'Dark' | 'System';

  @IsOptional()
  @IsEnum(['Daily', 'Weekly', 'Never'])
  emailDigest?: 'Daily' | 'Weekly' | 'Never';

  @IsOptional()
  @IsEnum(['Public', 'Private'])
  contentVisibility?: 'Public' | 'Private';

  @IsOptional()
  @IsBoolean()
  twoFactorEnabled?: boolean;

  @IsOptional()
  @IsEnum(['Short', 'Medium', 'Long'])
  sessionTimeout?: 'Short' | 'Medium' | 'Long';
}

// Main user profile update DTO (defined after dependencies)
export class UpdateUserProfileDto {
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(30)
  uname?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => UserProfileUpdateDto)
  profile?: UserProfileUpdateDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => UserPreferencesUpdateDto)
  prefs?: UserPreferencesUpdateDto;
}

// Public activity DTO (defined first)
export class PublicActivityDto {
  @IsDateString()
  lastActivityAt: Date;
}

// Public profile DTO (defined before PublicUserProfileDto)
export class PublicProfileDto {
  @IsString()
  displayName: string;

  @IsString()
  bio: string;

  @IsString()
  location: string;

  @IsString()
  website: string;

  @IsString()
  pic: string;

  @IsString()
  banner: string;

  @IsBoolean()
  isGuide: boolean;

  @IsBoolean()
  isVendor: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => SocialLinksDto)
  socialLinks?: SocialLinksDto;
}

// Public user profile DTO (defined after dependencies)
export class PublicUserProfileDto {
  @IsString()
  id: string;

  @IsString()
  uname: string;

  @ValidateNested()
  @Type(() => PublicProfileDto)
  profile: PublicProfileDto;

  @ValidateNested()
  @Type(() => PublicActivityDto)
  activity: PublicActivityDto;

  @IsDateString()
  createdAt: Date;
}

// User search DTOs
export class UserSearchQueryDto {
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  q: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  offset?: number = 0;
}

export class UserSearchResultDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PublicUserProfileDto)
  users: PublicUserProfileDto[];

  @IsNumber()
  total: number;

  @IsNumber()
  limit: number;

  @IsNumber()
  offset: number;

  @IsBoolean()
  hasMore: boolean;
}

// Follow-related DTOs
export class FollowersResponseDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PublicUserProfileDto)
  followers: PublicUserProfileDto[];

  @IsNumber()
  count: number;
}

export class FollowingResponseDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PublicUserProfileDto)
  following: PublicUserProfileDto[];

  @IsNumber()
  count: number;
}
