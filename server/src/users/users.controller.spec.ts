import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException, NotFoundException, BadRequestException } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User, UserStatus } from '@/types/global';

describe('UsersController', () => {
  let controller: UsersController;
  let usersService: jest.Mocked<UsersService>;

  const mockUser: User = {
    id: '123e4567-e89b-12d3-a456-************',
    uname: 'testuser.0001',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test Location',
      website: 'https://test.com',
      pic: 'https://test.com/pic.jpg',
      banner: 'https://test.com/banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'System',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 0,
      commentsToday: 0,
      votesToday: 0,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {
      github: 'github123',
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockRequest = {
    user: mockUser,
  } as any;

  beforeEach(async () => {
    const mockUsersService = {
      getUserById: jest.fn(),
      getUserByIdOrUsername: jest.fn(),
      updateUserProfile: jest.fn(),
      searchUsers: jest.fn(),
      followUser: jest.fn(),
      unfollowUser: jest.fn(),
      getUserFollowers: jest.fn(),
      getUserFollowing: jest.fn(),
      toPublicProfile: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get(UsersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getCurrentUser', () => {
    it('should return current user', async () => {
      const result = await controller.getCurrentUser(mockRequest);

      expect(result).toEqual(mockUser);
    });

    it('should throw ForbiddenException when user not authenticated', async () => {
      const requestWithoutUser = { user: null } as any;

      await expect(controller.getCurrentUser(requestWithoutUser))
        .rejects.toThrow(ForbiddenException);
    });
  });

  describe('updateCurrentUser', () => {
    it('should update current user profile', async () => {
      const updateData = {
        profile: {
          displayName: 'Updated Name',
        },
      };
      const updatedUser = {
        ...mockUser,
        profile: {
          ...mockUser.profile,
          ...updateData.profile,
        },
      };

      usersService.updateUserProfile.mockResolvedValue(updatedUser);

      const result = await controller.updateCurrentUser(mockRequest, updateData);

      expect(result).toEqual(updatedUser);
      expect(usersService.updateUserProfile).toHaveBeenCalledWith(mockUser.id, updateData);
    });
  });

  describe('getUserProfile', () => {
    it('should return public user profile by ID', async () => {
      const publicProfile = {
        id: mockUser.id,
        uname: mockUser.uname,
        profile: mockUser.profile,
        activity: { lastActivityAt: mockUser.activity.lastActivityAt },
        createdAt: mockUser.createdAt,
      };

      usersService.getUserByIdOrUsername.mockResolvedValue(mockUser);
      usersService.toPublicProfile.mockReturnValue(publicProfile);

      const result = await controller.getUserProfile(mockUser.id);

      expect(result).toEqual(publicProfile);
      expect(usersService.getUserByIdOrUsername).toHaveBeenCalledWith(mockUser.id);
    });

    it('should return public user profile by username', async () => {
      const publicProfile = {
        id: mockUser.id,
        uname: mockUser.uname,
        profile: mockUser.profile,
        activity: { lastActivityAt: mockUser.activity.lastActivityAt },
        createdAt: mockUser.createdAt,
      };

      usersService.getUserByIdOrUsername.mockResolvedValue(mockUser);
      usersService.toPublicProfile.mockReturnValue(publicProfile);

      const result = await controller.getUserProfile(mockUser.uname);

      expect(result).toEqual(publicProfile);
      expect(usersService.getUserByIdOrUsername).toHaveBeenCalledWith(mockUser.uname);
    });

    it('should throw NotFoundException when user not found', async () => {
      usersService.getUserByIdOrUsername.mockResolvedValue(null);

      await expect(controller.getUserProfile('nonexistent'))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('followUser', () => {
    it('should follow user successfully', async () => {
      const targetUser = { ...mockUser, id: 'target-id' };

      usersService.getUserByIdOrUsername.mockResolvedValue(targetUser);
      usersService.followUser.mockResolvedValue(undefined);

      const result = await controller.followUser(mockRequest, 'target-id');

      expect(result).toEqual({
        message: 'User followed successfully',
        following: true,
      });
      expect(usersService.followUser).toHaveBeenCalledWith(mockUser.id, 'target-id');
    });

    it('should throw BadRequestException when trying to follow yourself', async () => {
      usersService.getUserByIdOrUsername.mockResolvedValue(mockUser);

      await expect(controller.followUser(mockRequest, mockUser.id))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw NotFoundException when target user not found', async () => {
      usersService.getUserByIdOrUsername.mockResolvedValue(null);

      await expect(controller.followUser(mockRequest, 'nonexistent'))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('searchUsers', () => {
    it('should search users successfully', async () => {
      const searchQuery = { q: 'test', limit: 10, offset: 0 };
      const searchResult = {
        users: [],
        total: 0,
        limit: 10,
        offset: 0,
        hasMore: false,
      };

      usersService.searchUsers.mockResolvedValue(searchResult);

      const result = await controller.searchUsers(searchQuery);

      expect(result).toEqual(searchResult);
      expect(usersService.searchUsers).toHaveBeenCalledWith(searchQuery);
    });
  });
});
