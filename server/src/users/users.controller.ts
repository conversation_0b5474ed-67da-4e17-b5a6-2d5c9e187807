import {
  Controller,
  Get,
  Put,
  Post,
  Delete,
  Param,
  Body,
  Query,
  Req,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { Request } from 'express';
import { UsersService } from './users.service';
import {
  UpdateUserProfileDto,
  PublicUserProfileDto,
  UserSearchQueryDto,
  UserSearchResultDto,
  FollowersResponseDto,
  FollowingResponseDto,
} from './dto/users.dto';
import { User } from '@/types/global';

@Controller('v1/users')
export class UsersController {
  private readonly logger = new Logger(UsersController.name);

  constructor(private readonly usersService: UsersService) { }

  @Get('me')
  async getCurrentUser (@Req() req: Request): Promise<User> {
    const user = req.user as User;
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    return user;
  }

  @Put('me')
  async updateCurrentUser (
    @Req() req: Request,
    @Body() updateData: UpdateUserProfileDto,
  ): Promise<User> {
    const user = req.user as User;
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    return this.usersService.updateUserProfile(user.id, updateData);
  }

  @Get('search')
  async searchUsers (
    @Query() query: UserSearchQueryDto,
  ): Promise<UserSearchResultDto> {
    return this.usersService.searchUsers(query);
  }

  @Get('me/followers')
  async getMyFollowers (@Req() req: Request): Promise<FollowersResponseDto> {
    const user = req.user as User;
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    return this.usersService.getUserFollowers(user.id);
  }

  @Get('me/following')
  async getMyFollowing (@Req() req: Request): Promise<FollowingResponseDto> {
    const user = req.user as User;
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    return this.usersService.getUserFollowing(user.id);
  }

  @Get(':id')
  async getUserProfile (@Param('id') id: string): Promise<PublicUserProfileDto> {
    // This endpoint accepts both user ID and username
    const user = await this.usersService.getUserByIdOrUsername(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.usersService.toPublicProfile(user);
  }

  @Get(':userId/followers')
  async getUserFollowers (@Param('userId') userId: string): Promise<FollowersResponseDto> {
    // Verify user exists first
    const user = await this.usersService.getUserByIdOrUsername(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.usersService.getUserFollowers(user.id);
  }

  @Get(':userId/following')
  async getUserFollowing (@Param('userId') userId: string): Promise<FollowingResponseDto> {
    // Verify user exists first
    const user = await this.usersService.getUserByIdOrUsername(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.usersService.getUserFollowing(user.id);
  }

  @Post(':userId/follow')
  async followUser (
    @Req() req: Request,
    @Param('userId') targetUserId: string,
  ): Promise<{ message: string; following: boolean }> {
    const currentUser = req.user as User;
    if (!currentUser) {
      throw new ForbiddenException('User not authenticated');
    }

    // Resolve target user ID if username was provided
    const targetUser = await this.usersService.getUserByIdOrUsername(targetUserId);
    if (!targetUser) {
      throw new NotFoundException('User not found');
    }

    if (currentUser.id === targetUser.id) {
      throw new BadRequestException('Cannot follow yourself');
    }

    await this.usersService.followUser(currentUser.id, targetUser.id);
    return { message: 'User followed successfully', following: true };
  }

  @Delete(':userId/follow')
  async unfollowUser (
    @Req() req: Request,
    @Param('userId') targetUserId: string,
  ): Promise<{ message: string; following: boolean }> {
    const currentUser = req.user as User;
    if (!currentUser) {
      throw new ForbiddenException('User not authenticated');
    }

    // Resolve target user ID if username was provided
    const targetUser = await this.usersService.getUserByIdOrUsername(targetUserId);
    if (!targetUser) {
      throw new NotFoundException('User not found');
    }

    await this.usersService.unfollowUser(currentUser.id, targetUser.id);
    return { message: 'User unfollowed successfully', following: false };
  }
}
