import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { UserRepository } from '../database/repositories/user.repository';
import { UserEntity, UserFollowEntity } from '../database/entities';

@Module({
  imports: [TypeOrmModule.forFeature([UserEntity, UserFollowEntity])],
  controllers: [UsersController],
  providers: [UsersService, UserRepository],
  exports: [UsersService],
})
export class UsersModule { }
