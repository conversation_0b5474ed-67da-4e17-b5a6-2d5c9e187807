import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { UsersService } from './users.service';
import { UserRepository } from '../database/repositories/user.repository';
import { User, UserStatus } from '@/types/global';

describe('UsersService', () => {
  let service: UsersService;
  let userRepository: jest.Mocked<UserRepository>;

  const mockUser: User = {
    id: '123e4567-e89b-12d3-a456-************',
    uname: 'testuser.0001',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test Location',
      website: 'https://test.com',
      pic: 'https://test.com/pic.jpg',
      banner: 'https://test.com/banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'System',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 0,
      commentsToday: 0,
      votesToday: 0,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {
      github: 'github123',
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockUserRepository = {
      findUserById: jest.fn(),
      findUserByUsername: jest.fn(),
      findUserByIdOrUsername: jest.fn(),
      updateUser: jest.fn(),
      searchUsers: jest.fn(),
      countSearchResults: jest.fn(),
      createUserFollow: jest.fn(),
      removeUserFollow: jest.fn(),
      isUserFollowing: jest.fn(),
      getUserFollowers: jest.fn(),
      getUserFollowing: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userRepository = module.get(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserByIdOrUsername', () => {
    it('should find user by ID using OR query', async () => {
      userRepository.findUserByIdOrUsername.mockResolvedValue(mockUser);

      const result = await service.getUserByIdOrUsername(mockUser.id);

      expect(result).toEqual(mockUser);
      expect(userRepository.findUserByIdOrUsername).toHaveBeenCalledWith(mockUser.id);
    });

    it('should find user by username using OR query', async () => {
      userRepository.findUserByIdOrUsername.mockResolvedValue(mockUser);

      const result = await service.getUserByIdOrUsername(mockUser.uname);

      expect(result).toEqual(mockUser);
      expect(userRepository.findUserByIdOrUsername).toHaveBeenCalledWith(mockUser.uname);
    });

    it('should return null when user not found', async () => {
      userRepository.findUserByIdOrUsername.mockResolvedValue(null);

      const result = await service.getUserByIdOrUsername('nonexistent');

      expect(result).toBeNull();
      expect(userRepository.findUserByIdOrUsername).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('updateUserProfile', () => {
    it('should update user profile successfully', async () => {
      const updateData = {
        profile: {
          displayName: 'Updated Name',
          bio: 'Updated bio',
        },
      };
      const updatedUser = {
        ...mockUser,
        profile: {
          ...mockUser.profile,
          ...updateData.profile,
        },
      };

      userRepository.findUserById.mockResolvedValue(mockUser);
      userRepository.updateUser.mockResolvedValue(updatedUser);

      const result = await service.updateUserProfile(mockUser.id, updateData);

      expect(result).toEqual(updatedUser);
      expect(userRepository.updateUser).toHaveBeenCalledWith(mockUser.id, expect.objectContaining({
        profile: expect.objectContaining(updateData.profile),
      }));
    });

    it('should throw NotFoundException when user not found', async () => {
      userRepository.findUserById.mockResolvedValue(null);

      await expect(service.updateUserProfile('nonexistent', {}))
        .rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when username is already taken', async () => {
      const updateData = { uname: 'existinguser' };
      const existingUser = { ...mockUser, id: 'different-id' };

      userRepository.findUserById.mockResolvedValue(mockUser);
      userRepository.findUserByUsername.mockResolvedValue(existingUser);

      await expect(service.updateUserProfile(mockUser.id, updateData))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('followUser', () => {
    it('should follow user successfully', async () => {
      userRepository.isUserFollowing.mockResolvedValue(false);
      userRepository.createUserFollow.mockResolvedValue(undefined);

      await service.followUser('follower-id', 'following-id');

      expect(userRepository.createUserFollow).toHaveBeenCalledWith('follower-id', 'following-id');
    });

    it('should throw BadRequestException when already following', async () => {
      userRepository.isUserFollowing.mockResolvedValue(true);

      await expect(service.followUser('follower-id', 'following-id'))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('toPublicProfile', () => {
    it('should convert user to public profile', () => {
      const publicProfile = service.toPublicProfile(mockUser);

      expect(publicProfile).toEqual({
        id: mockUser.id,
        uname: mockUser.uname,
        profile: mockUser.profile,
        activity: {
          lastActivityAt: mockUser.activity.lastActivityAt,
        },
        createdAt: mockUser.createdAt,
      });

      // Ensure sensitive data is not included
      expect(publicProfile).not.toHaveProperty('email');
      expect(publicProfile).not.toHaveProperty('prefs');
      expect(publicProfile).not.toHaveProperty('providerAccounts');
    });
  });
});
