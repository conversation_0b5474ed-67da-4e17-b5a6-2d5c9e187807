import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { UserRepository } from '../database/repositories/user.repository';
import { User } from '@/types/global';
import {
  UpdateUserProfileDto,
  PublicUserProfileDto,
  UserSearchQueryDto,
  UserSearchResultDto,
  FollowersResponseDto,
  FollowingResponseDto,
} from './dto/users.dto';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(private readonly userRepository: UserRepository) { }

  async getUserById (id: string): Promise<User | null> {
    return this.userRepository.findUserById(id);
  }

  async getUserByUsername (username: string): Promise<User | null> {
    return this.userRepository.findUserByUsername(username);
  }

  /**
   * Get user by ID or username - supports both formats for flexible API usage
   * Uses a single OR query since UUIDs and usernames will never overlap
   */
  async getUserByIdOrUsername (identifier: string): Promise<User | null> {
    return this.userRepository.findUserByIdOrUsername(identifier);
  }

  async updateUserProfile (userId: string, updateData: UpdateUserProfileDto): Promise<User> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Validate username uniqueness if being updated
    if (updateData.uname && updateData.uname !== user.uname) {
      const existingUser = await this.getUserByUsername(updateData.uname);
      if (existingUser) {
        throw new BadRequestException('Username already taken');
      }
    }

    // Merge the update data with existing user data
    const updatedUserData: Partial<User> = {
      ...updateData,
      profile: {
        ...user.profile,
        ...updateData.profile,
      },
      prefs: {
        ...user.prefs,
        ...updateData.prefs,
      },
    };

    return this.userRepository.updateUser(userId, updatedUserData);
  }

  async searchUsers (query: UserSearchQueryDto): Promise<UserSearchResultDto> {
    const { q, limit = 20, offset = 0 } = query;

    if (!q || q.trim().length < 2) {
      throw new BadRequestException('Search query must be at least 2 characters');
    }

    const users = await this.userRepository.searchUsers(q.trim(), limit, offset);
    const total = await this.userRepository.countSearchResults(q.trim());

    return {
      users: users.map(user => this.toPublicProfile(user)),
      total,
      limit,
      offset,
      hasMore: offset + limit < total,
    };
  }

  async followUser (followerId: string, followingId: string): Promise<void> {
    // Check if already following
    const isFollowing = await this.userRepository.isUserFollowing(followerId, followingId);
    if (isFollowing) {
      throw new BadRequestException('Already following this user');
    }

    await this.userRepository.createUserFollow(followerId, followingId);
    this.logger.log(`User ${followerId} started following ${followingId}`);
  }

  async unfollowUser (followerId: string, followingId: string): Promise<void> {
    const isFollowing = await this.userRepository.isUserFollowing(followerId, followingId);
    if (!isFollowing) {
      throw new BadRequestException('Not following this user');
    }

    await this.userRepository.removeUserFollow(followerId, followingId);
    this.logger.log(`User ${followerId} unfollowed ${followingId}`);
  }

  async getUserFollowers (userId: string): Promise<FollowersResponseDto> {
    const followers = await this.userRepository.getUserFollowers(userId);
    return {
      followers: followers.map(user => this.toPublicProfile(user)),
      count: followers.length,
    };
  }

  async getUserFollowing (userId: string): Promise<FollowingResponseDto> {
    const following = await this.userRepository.getUserFollowing(userId);
    return {
      following: following.map(user => this.toPublicProfile(user)),
      count: following.length,
    };
  }

  /**
   * Convert a User object to a public profile (removes sensitive information)
   */
  toPublicProfile (user: User): PublicUserProfileDto {
    return {
      id: user.id,
      uname: user.uname,
      profile: {
        displayName: user.profile.displayName,
        bio: user.profile.bio,
        location: user.profile.location,
        website: user.profile.website,
        pic: user.profile.pic,
        banner: user.profile.banner,
        isGuide: user.profile.isGuide,
        isVendor: user.profile.isVendor,
        socialLinks: user.profile.socialLinks,
      },
      activity: {
        lastActivityAt: user.activity.lastActivityAt,
      },
      createdAt: user.createdAt,
    };
  }
}
