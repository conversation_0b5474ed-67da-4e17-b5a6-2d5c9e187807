{"extends": "../tsconfig.json", "compilerOptions": {"esModuleInterop": true, "module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./src", "rootDir": "./src", "incremental": true, "skipLibCheck": true, "strict": false, "strictNullChecks": false, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "strictPropertyInitialization": false, "paths": {"@/types/*": ["./types/*"], "@/*": ["./*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}