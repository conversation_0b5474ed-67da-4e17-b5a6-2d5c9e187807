'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../../contexts/AuthContext';
import { auth } from '../../../../lib/auth';

export default function GitHubCallbackPage () {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { refreshProfile } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Check for error in URL params first
        const urlParams = new URLSearchParams(window.location.search);
        const urlError = urlParams.get('error');

        if (urlError) {
          throw new Error(decodeURIComponent(urlError));
        }

        // Handle OAuth callback
        const token = auth.handleOAuthCallback();

        if (token) {
          // Token found, refresh profile
          await refreshProfile();
          setStatus('success');

          // Redirect to profile page after a brief delay
          setTimeout(() => {
            router.push('/me');
          }, 2000);
        } else {
          // No token found, but no error either - assume success with httpOnly cookie
          setStatus('success');

          setTimeout(() => {
            router.push('/me');
          }, 2000);
        }
      } catch (err) {
        console.error('OAuth callback error:', err);
        setError(err instanceof Error ? err.message : 'Authentication failed');
        setStatus('error');

        // Redirect to login after error
        setTimeout(() => {
          router.push('/auth/login');
        }, 3000);
      }
    };

    handleCallback();
  }, [router, refreshProfile]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ background: 'var(--background)' }}>
        <div className="text-center animate-fade-in">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 mx-auto" style={{ borderColor: 'var(--primary)' }}></div>
          <h2 className="mt-4 text-xl font-semibold" style={{ color: 'var(--foreground)' }}>
            🎣 Completing sign in...
          </h2>
          <p className="mt-2" style={{ color: 'var(--foreground-secondary)' }}>
            Please wait while we set up your fishing community account.
          </p>
        </div>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ background: 'var(--background)' }}>
        <div className="text-center animate-fade-in">
          <div
            className="mx-auto flex items-center justify-center h-16 w-16 rounded-full"
            style={{ background: 'var(--success)', opacity: 0.2 }}
          >
            <svg
              className="h-8 w-8"
              style={{ color: 'var(--success)' }}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h2 className="mt-4 text-xl font-semibold" style={{ color: 'var(--foreground)' }}>
            🎉 Sign in successful!
          </h2>
          <p className="mt-2" style={{ color: 'var(--foreground-secondary)' }}>
            Welcome to the fishing community! Redirecting you to your profile...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center" style={{ background: 'var(--background)' }}>
      <div className="text-center animate-fade-in">
        <div
          className="mx-auto flex items-center justify-center h-16 w-16 rounded-full"
          style={{ background: 'var(--error)', opacity: 0.2 }}
        >
          <svg
            className="h-8 w-8"
            style={{ color: 'var(--error)' }}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </div>
        <h2 className="mt-4 text-xl font-semibold" style={{ color: 'var(--foreground)' }}>
          ❌ Sign in failed
        </h2>
        <p className="mt-2" style={{ color: 'var(--foreground-secondary)' }}>
          {error || 'An unexpected error occurred during authentication.'}
        </p>
        <p className="mt-2 text-sm" style={{ color: 'var(--foreground-muted)' }}>
          Redirecting you back to the login page...
        </p>
      </div>
    </div>
  );
}
