@import "tailwindcss";

/* LMB Fishing Community - Dark Theme */
:root {
  /* Primary Colors - Ocean/Water Theme */
  --primary: #1e40af; /* Deep blue */
  --primary-hover: #1d4ed8; /* Brighter blue */
  --primary-light: #3b82f6; /* Light blue */

  /* Secondary Colors - Nature/Fishing Theme */
  --secondary: #059669; /* Emerald green */
  --secondary-hover: #047857; /* Darker green */
  --accent: #f59e0b; /* Amber/sunset */
  --accent-hover: #d97706; /* Darker amber */

  /* Background Colors */
  --background: #0f172a; /* Slate 900 - Deep night */
  --background-secondary: #1e293b; /* Slate 800 - Card backgrounds */
  --background-tertiary: #334155; /* Slate 700 - Elevated surfaces */
  --background-hover: #475569; /* Slate 600 - Hover states */

  /* Text Colors */
  --foreground: #f8fafc; /* Slate 50 - Primary text */
  --foreground-secondary: #cbd5e1; /* Slate 300 - Secondary text */
  --foreground-muted: #94a3b8; /* Slate 400 - Muted text */

  /* Border Colors */
  --border: #334155; /* Slate 700 - Default borders */
  --border-light: #475569; /* Slate 600 - Light borders */

  /* Status Colors */
  --success: #10b981; /* Emerald 500 */
  --warning: #f59e0b; /* Amber 500 */
  --error: #ef4444; /* Red 500 */
  --info: #3b82f6; /* Blue 500 */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition: all 0.2s ease-in-out;
}

/* Font Variables */
:root {
  --font-sans: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  --font-mono: "SF Mono", Monaco, Inconsolata, "Roboto Mono", Consolas,
    "Courier New", monospace;
}

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--background-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--background-hover);
}

/* Selection Styling */
::selection {
  background: var(--primary);
  color: var(--foreground);
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Link Styles */
a {
  color: var(--primary-light);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* Button Base Styles */
button {
  font-family: inherit;
  cursor: pointer;
  transition: var(--transition);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Input Base Styles */
input,
textarea,
select {
  font-family: inherit;
  background: var(--background-secondary);
  border: 1px solid var(--border);
  color: var(--foreground);
  transition: var(--transition);
}

input:focus,
textarea:focus,
select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

/* Card Styles */
.card {
  background: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-light);
}

/* Utility Classes */
.text-muted {
  color: var(--foreground-muted);
}

.text-secondary {
  color: var(--foreground-secondary);
}

.bg-secondary {
  background: var(--background-secondary);
}

.bg-tertiary {
  background: var(--background-tertiary);
}

.border-default {
  border-color: var(--border);
}

/* Loading Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}
