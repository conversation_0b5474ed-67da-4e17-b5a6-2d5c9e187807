'use client';

import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { apiClient } from '../../lib/api';

export default function MePage () {
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const router = useRouter();
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [apiError, setApiError] = useState<string | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, isLoading, router]);

  useEffect(() => {
    // Test API connection
    const checkHealth = async () => {
      try {
        const health = await apiClient.get('/v1/auth/health');
        setHealthStatus(health);
      } catch (error) {
        console.error('Health check failed:', error);
        setApiError('Failed to connect to API');
      }
    };

    // Only check health if user is authenticated and loaded
    if (!isLoading && isAuthenticated && user) {
      checkHealth();
    }
  }, [isLoading, isAuthenticated, user]);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
      // Redirect anyway
      router.push('/');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2" style={{ borderColor: 'var(--primary)' }}></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen" style={{ background: 'var(--background)' }}>
      {/* Navigation */}
      <nav className="shadow" style={{ background: 'var(--background-secondary)', borderBottom: '1px solid var(--border)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold" style={{ color: 'var(--foreground)' }}>
                🎣 LargemouthBass.com
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm" style={{ color: 'var(--foreground-secondary)' }}>
                Welcome, {user.profile.displayName}
              </span>
              <button
                onClick={handleLogout}
                className="px-4 py-2 rounded-md text-sm font-medium transition-colors"
                style={{
                  background: 'var(--error)',
                  color: 'var(--foreground)',
                  border: 'none'
                }}
                onMouseEnter={(e) => (e.target as HTMLElement).style.opacity = '0.9'}
                onMouseLeave={(e) => (e.target as HTMLElement).style.opacity = '1'}
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* User Profile Card */}
            <div className="card animate-fade-in">
              <div>
                <h3 className="text-lg leading-6 font-medium mb-4" style={{ color: 'var(--foreground)' }}>
                  Your Profile
                </h3>
                <div className="flex items-center space-x-4">
                  {user.profile.pic && (
                    <img
                      className="h-16 w-16 rounded-full border-2"
                      style={{ borderColor: 'var(--border)' }}
                      src={user.profile.pic}
                      alt={user.profile.displayName}
                    />
                  )}
                  <div>
                    <h4 className="text-lg font-semibold" style={{ color: 'var(--foreground)' }}>
                      {user.profile.displayName}
                    </h4>
                    <p className="text-sm" style={{ color: 'var(--foreground-secondary)' }}>@{user.uname}</p>
                    <p className="text-sm" style={{ color: 'var(--foreground-secondary)' }}>{user.email}</p>
                  </div>
                </div>
                {user.profile.bio && (
                  <p className="mt-4 text-sm" style={{ color: 'var(--foreground-secondary)' }}>{user.profile.bio}</p>
                )}
                {user.profile.location && (
                  <p className="mt-2 text-sm" style={{ color: 'var(--foreground-muted)' }}>
                    📍 {user.profile.location}
                  </p>
                )}
                {user.profile.website && (
                  <p className="mt-2 text-sm" style={{ color: 'var(--foreground-muted)' }}>
                    🌐 <a
                      href={user.profile.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ color: 'var(--primary-light)' }}
                      className="hover:underline"
                    >
                      {user.profile.website}
                    </a>
                  </p>
                )}
              </div>
            </div>

            {/* API Status Card */}
            <div className="card animate-fade-in">
              <div>
                <h3 className="text-lg leading-6 font-medium mb-4" style={{ color: 'var(--foreground)' }}>
                  API Connection Status
                </h3>
                {apiError ? (
                  <div style={{ color: 'var(--error)' }}>
                    <p className="font-medium">❌ Connection Failed</p>
                    <p className="text-sm mt-1">{apiError}</p>
                  </div>
                ) : healthStatus ? (
                  <div style={{ color: 'var(--success)' }}>
                    <p className="font-medium">✅ Connected</p>
                    <p className="text-sm mt-1">API is responding normally</p>
                    <div className="mt-3 text-xs" style={{ color: 'var(--foreground-muted)' }}>
                      <pre
                        className="p-2 rounded text-xs overflow-x-auto"
                        style={{
                          background: 'var(--background-tertiary)',
                          border: '1px solid var(--border)'
                        }}
                      >
                        {JSON.stringify(healthStatus, null, 2)}
                      </pre>
                    </div>
                  </div>
                ) : (
                  <div style={{ color: 'var(--warning)' }}>
                    <p className="font-medium">⏳ Checking...</p>
                  </div>
                )}
              </div>
            </div>

            {/* Account Details Card */}
            <div className="card animate-fade-in md:col-span-2">
              <div>
                <h3 className="text-lg leading-6 font-medium mb-4" style={{ color: 'var(--foreground)' }}>
                  Account Details
                </h3>
                <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium" style={{ color: 'var(--foreground-muted)' }}>User ID</dt>
                    <dd className="mt-1 text-sm font-mono" style={{ color: 'var(--foreground)' }}>{user.id}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium" style={{ color: 'var(--foreground-muted)' }}>Status</dt>
                    <dd className="mt-1 text-sm">
                      <span
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        style={{
                          background: 'var(--success)',
                          color: 'var(--foreground)'
                        }}
                      >
                        {user.status}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium" style={{ color: 'var(--foreground-muted)' }}>Email Verified</dt>
                    <dd className="mt-1 text-sm" style={{ color: 'var(--foreground)' }}>
                      {user.emailVerified ? '✅ Yes' : '❌ No'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium" style={{ color: 'var(--foreground-muted)' }}>Last Login</dt>
                    <dd className="mt-1 text-sm" style={{ color: 'var(--foreground)' }}>
                      {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : 'N/A'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium" style={{ color: 'var(--foreground-muted)' }}>Member Since</dt>
                    <dd className="mt-1 text-sm" style={{ color: 'var(--foreground)' }}>
                      {new Date(user.createdAt).toLocaleDateString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium" style={{ color: 'var(--foreground-muted)' }}>Connected Accounts</dt>
                    <dd className="mt-1 text-sm" style={{ color: 'var(--foreground)' }}>
                      {user.providerAccounts.github && '🐙 GitHub'}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
