'use client';

import { useAuth } from '../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function Home () {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  // Home page should be accessible to everyone - no redirects

  return (
    <div className="min-h-screen" style={{ background: 'var(--background)' }}>
      {/* Navigation */}
      <nav className="shadow" style={{ background: 'var(--background-secondary)', borderBottom: '1px solid var(--border)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold" style={{ color: 'var(--foreground)' }}>
                🎣 LargemouthBass.com
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {isLoading ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2" style={{ borderColor: 'var(--primary)' }}></div>
              ) : isAuthenticated && user ? (
                <div className="flex items-center space-x-4">
                  <span className="text-sm" style={{ color: 'var(--foreground-secondary)' }}>
                    Welcome, {user.profile.displayName}
                  </span>
                  <button
                    onClick={() => router.push('/me')}
                    className="px-4 py-2 rounded-md text-sm font-medium transition-colors"
                    style={{
                      background: 'var(--primary)',
                      color: 'var(--primary-foreground)',
                    }}
                  >
                    My Profile
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => router.push('/auth/login')}
                  className="px-4 py-2 rounded-md text-sm font-medium transition-colors"
                  style={{
                    background: 'var(--primary)',
                    color: 'var(--primary-foreground)',
                  }}
                >
                  Sign In
                </button>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-6xl" style={{ color: 'var(--foreground)' }}>
            Welcome to LargemouthBass.com
          </h1>
          <p className="mt-6 text-lg leading-8" style={{ color: 'var(--foreground-secondary)' }}>
            The ultimate fishing community platform for anglers. Connect with fellow fishers,
            discover new fishing spots, and share your catches.
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            {!isAuthenticated && !isLoading && (
              <button
                onClick={() => router.push('/auth/login')}
                className="px-6 py-3 text-lg font-semibold rounded-md transition-colors"
                style={{
                  background: 'var(--primary)',
                  color: 'var(--primary-foreground)',
                }}
              >
                Get Started
              </button>
            )}
            <a
              href="#features"
              className="text-lg font-semibold leading-6 transition-colors"
              style={{ color: 'var(--foreground)' }}
            >
              Learn more <span aria-hidden="true">→</span>
            </a>
          </div>
        </div>

        {/* Features Section */}
        <div id="features" className="mt-20">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-lg" style={{ background: 'var(--background-secondary)' }}>
              <div className="text-4xl mb-4">🎣</div>
              <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--foreground)' }}>
                Fishing Communities
              </h3>
              <p style={{ color: 'var(--foreground-secondary)' }}>
                Join communities of like-minded anglers and share your passion for fishing.
              </p>
            </div>
            <div className="text-center p-6 rounded-lg" style={{ background: 'var(--background-secondary)' }}>
              <div className="text-4xl mb-4">📍</div>
              <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--foreground)' }}>
                Fishing Spots
              </h3>
              <p style={{ color: 'var(--foreground-secondary)' }}>
                Discover new fishing locations and share your favorite spots with the community.
              </p>
            </div>
            <div className="text-center p-6 rounded-lg" style={{ background: 'var(--background-secondary)' }}>
              <div className="text-4xl mb-4">📱</div>
              <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--foreground)' }}>
                Share Your Catches
              </h3>
              <p style={{ color: 'var(--foreground-secondary)' }}>
                Post photos and stories of your fishing adventures and connect with other anglers.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
