'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User } from '@/types/global';
import { auth, AuthState } from '../lib/auth';

interface AuthContextType extends AuthState {
  login: () => void;
  logout: () => Promise<void>;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider ({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);

  // Load user profile on mount
  useEffect(() => {
    // Only load profile once on mount
    let mounted = true;

    const loadProfile = async () => {
      if (mounted) {
        await loadUserProfile();
      }
    };

    loadProfile();

    return () => {
      mounted = false;
    };
  }, []); // Empty dependency array - only run once

  const loadUserProfile = async () => {
    // Prevent multiple simultaneous profile loads
    if (isLoadingProfile) {
      return;
    }

    setIsLoadingProfile(true);
    setIsLoading(true);

    try {
      // Try to get profile - this will use httpOnly cookies if available
      const profile = await auth.getProfile();

      if (profile) {
        setUser(profile);
        setIsAuthenticated(true);
      } else {
        // No profile returned, user not authenticated
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Failed to load user profile:', error);
      // Only clear token if it's a 401 error, otherwise might be network issue
      if (error.response?.status === 401) {
        auth.clearToken();
      }
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
      setIsLoadingProfile(false);
    }
  };

  const login = () => {
    // Redirect to GitHub OAuth
    window.location.href = auth.getGitHubAuthUrl();
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await auth.logoutComplete();
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout failed:', error);
      // Still clear local state even if logout fails
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshProfile = async () => {
    await loadUserProfile();
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth (): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
