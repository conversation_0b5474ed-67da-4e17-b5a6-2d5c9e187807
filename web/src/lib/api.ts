import axios, { AxiosInstance, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';

// API client configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

class ApiClient {
  private client: AxiosInstance;
  private csrfToken: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      withCredentials: true, // Important for httpOnly cookies
      timeout: 10000, // 10 second timeout
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      async (config) => {
        // Add CSRF token for state-changing requests
        if (['post', 'put', 'patch', 'delete'].includes(config.method?.toLowerCase() || '')) {
          await this.ensureCsrfToken();
          if (this.csrfToken) {
            config.headers['X-CSRF-Token'] = this.csrfToken;
          }
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Clear auth state on unauthorized, but don't redirect
          // Let the AuthContext handle the redirect logic
          Cookies.remove('auth-token');
        }
        return Promise.reject(error);
      }
    );
  }

  // CSRF token management
  private async ensureCsrfToken (): Promise<void> {
    if (!this.csrfToken) {
      try {
        const response = await this.client.get('/v1/security/csrf-token');
        this.csrfToken = response.data.csrfToken;
      } catch (error) {
        console.warn('Failed to get CSRF token:', error);
      }
    }
  }

  async getCsrfToken (): Promise<string | null> {
    await this.ensureCsrfToken();
    return this.csrfToken;
  }

  // Auth endpoints
  async getProfile () {
    const response = await this.client.get('/v1/auth/profile');
    return response.data;
  }

  async logout () {
    const response = await this.client.post('/v1/auth/logout');
    Cookies.remove('auth-token');
    return response.data;
  }

  async logoutComplete () {
    // This calls the same endpoint, but the backend now revokes OAuth tokens
    const response = await this.client.post('/v1/auth/logout');
    Cookies.remove('auth-token');
    return response.data;
  }

  // Health check
  async healthCheck () {
    const response = await this.client.get('/v1/health');
    return response.data;
  }

  // Generic methods
  async get<T = any> (url: string): Promise<AxiosResponse<T>> {
    return this.client.get(url);
  }

  async post<T = any> (url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.client.post(url, data);
  }

  async put<T = any> (url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.client.put(url, data);
  }

  async delete<T = any> (url: string): Promise<AxiosResponse<T>> {
    return this.client.delete(url);
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
