import { User } from '@/types/global';
import Cookies from 'js-cookie';
import { apiClient } from './api';

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    uname: string;
    email: string;
    profile: {
      displayName: string;
      pic: string;
      bio?: string;
      location?: string;
      website?: string;
    };
  };
}

// Auth utility functions
export const auth = {
  // Check if user is authenticated
  // Note: With httpOnly cookies, we can't check the token directly
  // This is mainly used as a fallback - the real auth state is managed by the context
  isAuthenticated (): boolean {
    if (typeof window === 'undefined') return false;
    // Check for any fallback token in localStorage (for development)
    const token = Cookies.get('auth-token');
    return !!token;
  },

  // Get stored token
  getToken (): string | undefined {
    if (typeof window === 'undefined') return undefined;
    return Cookies.get('auth-token');
  },

  // Store token (fallback for non-httpOnly scenarios)
  setToken (token: string): void {
    Cookies.set('auth-token', token, {
      expires: 7, // 7 days
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });
  },

  // Clear token
  clearToken (): void {
    Cookies.remove('auth-token');
  },

  // Get user profile
  async getProfile (): Promise<User | null> {
    try {
      const response = await apiClient.getProfile();
      return response;
    } catch (error) {
      console.error('Failed to get profile:', error);
      return null;
    }
  },

  // Logout
  async logout (): Promise<void> {
    try {
      await apiClient.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearToken();
    }
  },

  // Complete logout that also clears GitHub session
  async logoutComplete (): Promise<void> {
    try {
      // First, logout from our backend (this will revoke GitHub tokens)
      await apiClient.logoutComplete();

      // Clear our local tokens
      this.clearToken();

      // Open GitHub logout in a hidden iframe to clear GitHub session
      // This helps ensure the user is fully logged out from GitHub
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = 'https://github.com/logout';
      document.body.appendChild(iframe);

      // Remove iframe after a short delay
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 1000);

    } catch (error) {
      console.error('Complete logout error:', error);
      // Still clear local tokens even if logout fails
      this.clearToken();
    }
  },

  // GitHub OAuth URLs
  getGitHubAuthUrl (): string {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    return `${apiUrl}/v1/auth/github?web=true`;
  },

  // Handle OAuth callback (extract token from URL or cookie)
  handleOAuthCallback (): string | null {
    if (typeof window === 'undefined') return null;

    // Check URL params for token (fallback)
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');

    if (token) {
      this.setToken(token);
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      return token;
    }

    // Check if token is already in cookies (httpOnly scenario)
    return this.getToken() || null;
  },
};

export default auth;
